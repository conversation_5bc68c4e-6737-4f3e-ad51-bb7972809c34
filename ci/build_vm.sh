#!/usr/bin/env bash

mvn_build() {
  echo "mvn build ..."
  mvn $1
  if [ $? -eq 0 ]; then
    echo "mvn build success!"
  else
    echo "mvn build fail!"
    exit 1
  fi
}

echo "Building started, BUILDVERSION: ${BUILDVERSION}"
WORKDIR=$(pwd)
OUTPUT=${WORKDIR}/output
TMP=${WORKDIR}/tmp/
mkdir -p $OUTPUT
mkdir -p $TMP
rm -rf $TMP/*
echo "WORKDIR: $WORKDIR"
echo "OUTPUT: $OUTPUT"

mvn_build "install -Dmaven.test.skip=true -ntp -Dartifactory.publish.artifacts=false"

if [ "$isProd" == "false" ]; then
  cp ${MODULE_NAME}/target/${MODULE_NAME}.jar $OUTPUT/${MODULE_NAME}-${BUILDVERSION}.jar
  echo "${MODULE_NAME}@$env build success"
else
  MAJORVER=${VERSION}
  DATE=$(date +%m%d)
  fullVersion=$(git rev-parse HEAD)
  REVISION=$(git rev-parse HEAD | cut -c-8)
  BRANCH=$(git symbolic-ref HEAD | sed 's!refs\/heads\/!!')
  PACKAGE_VERSION=$MAJORVER.$BUILD_NUMBER.${DATE}.$REVISION
  echo "Package version: $PACKAGE_VERSION"

  echo "Build Version: $PACKAGE_VERSION" >>${TMP}version.txt
  dt=$(date +%Y-%m-%d)
  tm=$(date +%H:%M:%S)
  echo "Build Time: $dt $tm" >>${TMP}version.txt
  echo "Repository: Git" >>${TMP}version.txt
  echo "Branch: $BRANCH" >>${TMP}version.txt
  echo "Commit SHA: $fullVersion" >>${TMP}version.txt

  PROFILE="prod"

  cd ${MODULE_NAME}

  echo "Profile: $PROFILE"
  cp -f target/${MODULE_NAME}.jar ${TMP}${MODULE_NAME}.jar
  cp -f target/classes/application.properties ${TMP}application.properties
  cp -f target/classes/application-${PROFILE}.properties ${TMP}application-${PROFILE}.properties
  cp -f target/classes/logback.xml ${TMP}logback.xml
  if [ -s target/classes/csms-${PROFILE}.properties ]; then
    cp -f target/classes/csms-${PROFILE}.properties ${TMP}csms.properties
  fi

  cp -f bin/service.sh ${TMP}service.sh

  PACKAGE_NAME=${MODULE_NAME}_$PACKAGE_VERSION
  echo "PACKAGE_NAME: ${PACKAGE_NAME}"
  cd ${TMP}
  tar cvf ${PACKAGE_NAME}.tar.gz ${MODULE_NAME}.jar service.sh application.properties application-${PROFILE}.properties logback.xml version.txt csms.properties
  cp ${PACKAGE_NAME}.tar.gz $OUTPUT/${PACKAGE_NAME}.tar.gz

  echo "${PACKAGE_NAME}@$env build success"
fi

#!/usr/bin/env sh

set -e

export JAVA_HOME=${JAVA_HOME_17}
export PATH="${JAVA_HOME}/bin:$PATH"

# version.txt and create output directory
mkdir -p cube-executor-pull-query/src/main/resources/static/
mkdir -p output/report

if [ -f "version.txt" ]; then
  cp version.txt cube-executor-pull-query/src/main/resources/static/version.txt
fi

# build executable jar and move results into 'output'
#mvn -f cube-executor-pull-query clean package -Dmaven.test.skip=true
mvn install -pl cube-executor-pull-query -am  -Dmaven.test.skip=true -ntp -Dartifactory.publish.artifacts=false
cp cube-executor-pull-query/target/cube-executor-pull-query.jar output/cube-executor-pull-query-"${VERSION}"."${BUILD_NUMBER}".jar
cp -f cube-executor-pull-query/ci/Dockerfile ./ci/
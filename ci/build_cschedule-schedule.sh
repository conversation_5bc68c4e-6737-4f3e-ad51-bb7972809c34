#!/usr/bin/env sh

set -e

export JAVA_HOME=${JAVA_HOME_17}
export PATH="${JAVA_HOME}/bin:$PATH"

# version.txt and create output directory
mkdir -p cschedule-schedule/src/main/resources/static/
mkdir -p output/report

if [ -f "version.txt" ];then
  cp version.txt cschedule-schedule/src/main/resources/static/version.txt
fi

# build executable jar and move results into 'output'
mvn clean package -Dmaven.test.skip=true
cp cschedule-schedule/target/cschedule-schedule.jar output/cschedule-schedule-${BUILDVERSION}.jar
cp -f cschedule-schedule/ci/Dockerfile ./ci/
#!/usr/bin/env sh

set -e

export JAVA_HOME=${JAVA_HOME_17}
export PATH="${JAVA_HOME}/bin:$PATH"

if [ -z "$MODULE_NAME" ]; then
  echo "MODULE_NAME is empty. Please provide a valid module name."
else
  case "$MODULE_NAME" in
    "cube-scheduler-lib")
        sh ./ci/build_schedule_lib_module.sh
      ;;
    *)
      if [ "$isContainer" == "true" ]; then
        sh ./ci/build_container.sh
      else
        sh ./ci/build_vm.sh
      fi
      ;;
  esac
fi
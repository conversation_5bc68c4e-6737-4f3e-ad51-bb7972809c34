#!/usr/bin/env sh

set -e

export JAVA_HOME=${JAVA_HOME_17}
export PATH="${JAVA_HOME}/bin:$PATH"

# version.txt and create output directory
mkdir -p cube-scheduler-admin/src/main/resources/static/
mkdir -p output/report

if [ -f "version.txt" ]; then
  cp version.txt cube-scheduler-admin/src/main/resources/static/version.txt
fi

# build executable jar and move results into 'output'
#mvn -f cube-scheduler-admin clean package -Dmaven.test.skip=true
mvn install -pl cube-scheduler-admin -am  -Dmaven.test.skip=true -ntp -Dartifactory.publish.artifacts=false
cp cube-scheduler-admin/target/cube-scheduler-admin.jar output/cube-scheduler-admin-"${VERSION}"."${BUILD_NUMBER}".jar
cp -f cube-scheduler-admin/ci/Dockerfile ./ci/
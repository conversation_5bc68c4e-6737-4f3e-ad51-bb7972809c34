<?xml version="1.0" encoding="UTF-8"?>
<configuration>

    <property resource="application.properties"/>
    <property name="TRACE" value="%replace([%X{trace_id:-}:%X{span_id:-}]){'\[:\]', ''}"/>
    <property name="PATTERN" value="%d{yyyy-MM-dd HH:mm:ss.SSS}${TRACE}[%t][%p][%c{0}] - %m%n"/>
    <property name="MODULE" value="schedule" />
    <springProperty scope="context" name="LOG_PATH" source="log.filePath"/>


    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>%boldGreen(%date{ISO8601}) ${TRACE} %highlight([%-5level]) %boldCyan([%file]) %boldCyan([%line]) %yellow([%thread]) %boldMagenta(%logger{10}) %msg%n
            </pattern>
        </encoder>
    </appender>

    <appender name="INFO_APPENDER" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_PATH:-logs}/${MODULE}-info.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${LOG_PATH:-logs}/%d{yyyy-MM-dd,aux}/${MODULE}-info.%d{yyyy-MM-dd_HH}.%i.log.gz
            </fileNamePattern>
            <maxFileSize>${log.maxFileSize:-100MB}</maxFileSize>
            <maxHistory>30</maxHistory>
            <totalSizeCap>${log.totalSizeCap:-2GB}</totalSizeCap>
        </rollingPolicy>
        <encoder>
            <pattern>%date{ISO8601} [%level] [%file] [%line] [%thread] %logger{10} %msg%n</pattern>
        </encoder>
    </appender>

    <appender name="ERROR_APPENDER" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_PATH:-logs}/${MODULE}-error.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${LOG_PATH:-logs}/%d{yyyy-MM-dd,aux}/${MODULE}-error.%d{yyyy-MM-dd_HH}.%i.log.gz
            </fileNamePattern>
            <maxFileSize>${log.maxFileSize:-100MB}</maxFileSize>
            <maxHistory>30</maxHistory>
            <totalSizeCap>${log.totalSizeCap:-2GB}</totalSizeCap>
        </rollingPolicy>
        <encoder>
            <pattern>%date{ISO8601} [%level] [%file] [%line] [%thread] %logger{10} %msg%n</pattern>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>ERROR</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
    </appender>

    <appender name="MONITOR_APPENDER" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_PATH:-logs}/${MODULE}-monitor.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${LOG_PATH:-logs}/%d{yyyy-MM-dd,aux}/${MODULE}-monitor.%d{yyyy-MM-dd_HH}.%i.log.gz
            </fileNamePattern>
            <maxFileSize>${log.maxFileSize:-100MB}</maxFileSize>
            <maxHistory>30</maxHistory>
            <totalSizeCap>${log.totalSizeCap:-2GB}</totalSizeCap>
        </rollingPolicy>
        <encoder>
            <pattern>%msg%n</pattern>
        </encoder>
    </appender>


    <logger name="Monitor" level="TRACE" additivity="false">
        <appender-ref ref="MONITOR_APPENDER"/>
    </logger>

    <root level="info">
        <appender-ref ref="STDOUT"/>
        <appender-ref ref="INFO_APPENDER"/>
        <appender-ref ref="ERROR_APPENDER"/>
    </root>
</configuration>
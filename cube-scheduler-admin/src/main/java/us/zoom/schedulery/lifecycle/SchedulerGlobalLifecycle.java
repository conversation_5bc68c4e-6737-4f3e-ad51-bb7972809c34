package us.zoom.schedulery.lifecycle;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;

/**
 * @author: <PERSON><PERSON>
 * @date: 2025/3/26 14:03
 * @desc:
 */
@Slf4j
@Component
public class SchedulerGlobalLifecycle implements InitializingBean, DisposableBean {

    @Override
    public void afterPropertiesSet() throws Exception {

    }

    @Override
    public void destroy() throws Exception {

    }


}

package us.zoom.schedulery.config;

import com.zaxxer.hikari.HikariDataSource;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.context.EnvironmentAware;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.core.env.Environment;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;

import javax.sql.DataSource;

/**
 * @author: <PERSON><PERSON> Ding
 * @date: 2025/4/22 11:18
 * @desc:
 */
@MapperScan(basePackages = "us.zoom.schedulery.dao")
@Configuration
public class DatabaseConfiguration implements EnvironmentAware {

    private Environment environment;

    @Override
    public void setEnvironment(Environment environment) {
        this.environment = environment;
    }

    @Primary
    @Bean
    public DataSource dataSource() {
        HikariDataSource ds = new HikariDataSource();
        ds.setJdbcUrl(environment.getProperty("datasource.url"));
        ds.setUsername(environment.getProperty("cube.dataSource.username"));
        ds.setPassword(environment.getProperty("cube.dataSource.password"));
        ds.setMaximumPoolSize(environment.getProperty("dataSource.maxActive", Integer.class));
        ds.setMinimumIdle(environment.getProperty("dataSource.minIdle", Integer.class));
        ds.setCsmsRotateUsernameKey(environment.getProperty("dataSource.csms_rotate_username_key"));
        ds.setCsmsRotatePasswordKey(environment.getProperty("dataSource.csms_rotate_password_key"));
        ds.setPoolName("Scheduler-Admin");

        return ds;
    }

    @Bean
    public DataSourceTransactionManager dataSourceTransactionManager() throws Exception {
        return new DataSourceTransactionManager(dataSource());
    }



}

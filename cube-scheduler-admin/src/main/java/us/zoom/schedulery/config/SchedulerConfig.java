package us.zoom.schedulery.config;

import lombok.Data;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

/**
 * @author: <PERSON><PERSON> Ding
 * @date: 2025/3/26 17:19
 * @desc:
 */
@Data
@Configuration
public class SchedulerConfig implements InitializingBean {

    @Value("${cube.scheduler.triggerpool.fast.max}")
    private int triggerPoolFastMax;

    @Value("${cube.scheduler.triggerpool.slow.max}")
    private int triggerPoolSlowMax;


    public static SchedulerConfig config;

    @Override
    public void afterPropertiesSet() throws Exception {
        this.config = this;
    }

    public static SchedulerConfig getConfig() {
        return config;
    }

}

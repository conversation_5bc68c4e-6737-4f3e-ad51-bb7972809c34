package us.zoom.schedulery.config;

import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.EnvironmentAware;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;
import us.zoom.cloud.secrets.spring.annotation.SecretValue;
import us.zoom.schedulery.service.AsyncMQInstance;


@Component
public class AsyncmqConfig implements InitializingBean, EnvironmentAware {

    @Value("${asyncmq.endpoint}")
    private String asyncMqEndpoint;

    @Value("${asyncmq.username}")
    private String asyncMqUsername;

    @SecretValue("scheduler.asyncmq.password")
    private String asyncMqPassword;


    @Override
    public void afterPropertiesSet() throws Exception {
        AsyncMQInstance.init(asyncMqEndpoint, asyncMqUsername, asyncMqPassword);
    }

    Environment environment;

    @Override
    public void setEnvironment(Environment environment) {
        this.environment = environment;
    }
}

package us.zoom.schedulery.config;

import com.google.common.collect.Maps;
import org.springframework.beans.factory.annotation.Configurable;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import us.zoom.schedulery.filter.JwtAuthFilter;

import java.util.Map;

@Configurable
public class JwtConfig {


/*
    @Bean
    public FilterRegistrationBean<JwtAuthFilter> authFilter() {
        FilterRegistrationBean<JwtAuthFilter> authFilterFilterRegistrationBean = new FilterRegistrationBean<>();
        authFilterFilterRegistrationBean.setFilter(new JwtAuthFilter());
        authFilterFilterRegistrationBean.addUrlPatterns("/job/*");

        Map<String, String> initMap = Maps.newHashMap();
        initMap.put(JWTAuthFilter.CSMS_JWT_PUBLIC_KEY_PATH_LIST, environment.getProperty(JWTAuthFilter.CSMS_JWT_PUBLIC_KEY_PATH_LIST));
        authFilterFilterRegistrationBean.setInitParameters(initMap);

        authFilterFilterRegistrationBean.setName("ETEAuthFilter");
        authFilterFilterRegistrationBean.setOrder(1);
        return authFilterFilterRegistrationBean;
    }*/

}

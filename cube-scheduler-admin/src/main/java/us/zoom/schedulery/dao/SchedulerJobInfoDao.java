package us.zoom.schedulery.dao;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import us.zoom.cube.scheduler.lib.model.SchedulerJobInfo;

import java.util.List;

/**
 * @author: <PERSON><PERSON>
 * @date: 2025/4/21 21:43
 * @desc: It is strongly discouraged to update all fields of the Job Info; it's recommended to update only the necessary fields.
 */
@Mapper
public interface SchedulerJobInfoDao {

    //update enable and trigger time
    @Update("UPDATE cube_scheduler_job_info SET enabled = #{enabled}, trigger_last_time = #{triggerLastTime}, trigger_next_time = #{triggerNextTime}," +
            " update_time = #{updateTime} WHERE id = #{id}")
    void updateEnableAndTriggerTime(SchedulerJobInfo schedulerJobInfo);


    @Select("select * from cube_scheduler_job_info where enabled=1 and trigger_next_time <= #{maxNextTime} order by id limit #{preReadCount}")
    List<SchedulerJobInfo> scheduleJobQuery(@Param("maxNextTime") long maxNextTime, @Param("preReadCount") int preReadCount);


    @Select("select * from cube_scheduler_job_info where id = #{id}")
    SchedulerJobInfo getById(@Param("id") String id);

}

package us.zoom.schedulery.controller;

import com.fasterxml.jackson.databind.json.JsonMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.zaxxer.hikari.HikariDataSource;
import com.zaxxer.hikari.HikariPoolMXBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.sql.DataSource;

/**
 * @author: Starls Ding
 * @date: 2025/4/30 16:12
 * @desc:
 */
@RestController
@RequestMapping("/dataSource")
public class DataSourceController {

    @Autowired
    private DataSource dataSource;

    JsonMapper jsonMapper = new JsonMapper();

    @GetMapping("/info")
    public String info() {
        HikariDataSource hikariDataSource = (HikariDataSource) dataSource;
        HikariPoolMXBean hikariPoolMXBean = hikariDataSource.getHikariPoolMXBean();

        ObjectNode objectNode = jsonMapper.createObjectNode();
        objectNode.put("Active Connections", hikariPoolMXBean.getActiveConnections());
        objectNode.put("Idle Connections", hikariPoolMXBean.getIdleConnections());
        objectNode.put("Total Connections", hikariPoolMXBean.getTotalConnections());
        objectNode.put("Thread Waiting", hikariPoolMXBean.getThreadsAwaitingConnection());
        objectNode.put("Maximum Pool Size", hikariDataSource.getMaximumPoolSize());
        objectNode.put("Minimum Idle", hikariDataSource.getMinimumIdle());

        //make ObjectNode to json string
        return objectNode.toString();
    }

}

package us.zoom.schedulery;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.Import;
import us.zoom.cloud.secrets.spring.context.CSMSBeanDefinitionRegistrar;

/**
 * @author: <PERSON><PERSON> Ding
 * @date: 2025/3/19 15:51
 * @desc:
 */
@Import({CSMSBeanDefinitionRegistrar.class})
@SpringBootApplication
public class SchedulerAdminApplication {

    public static void main(String[] args) {
        SpringApplication.run(SchedulerAdminApplication.class, args);
    }

}
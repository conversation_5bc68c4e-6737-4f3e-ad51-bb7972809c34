package us.zoom.schedulery.utils;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;

/**
 * jacson json utils
 */

@Slf4j
public class JsonUtils {

    private static ObjectMapper mapper = new ObjectMapper();

    static {
        mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    }

    public static String toJsonString(Object object) {

        if (null == object) {
            return null;
        }
        try {
            return mapper.writeValueAsString(object);
        } catch (JsonProcessingException e) {
            log.error("toJsonStringerror, input is {} ", object, e);
            throw new RuntimeException(e);
        }
    }


    public static <T> T toObject(String input, Class<T> classMeta) {
        try {
            return mapper.readValue(input, classMeta);
        } catch (IOException e) {
            log.error("to object error, input is {} ", input, e);
            throw new RuntimeException(e);
        }
    }

    public static <T> T toObjectByTypeRef(String input, TypeReference<T> typeReference) {
        try {
            return mapper.readValue(input, typeReference);
        } catch (IOException e) {
            log.error("to toObjectByTypeRef error, input is {} ", input, e);
            throw new RuntimeException(e);
        }
    }

    public static String toJsonStringIgnoreExp(Object value) {
        try {
            return mapper.writeValueAsString(value);
        } catch (JsonProcessingException e) {
        }
        return null;
    }


}

package us.zoom.schedulery.utils;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.SQLException;

/**
 * @author: <PERSON><PERSON> Ding
 * @date: 2025/4/25 17:12
 * @desc:
 */
@Slf4j
@Component
public class DataSourceHelper implements InitializingBean {

    private static DataSourceHelper instance;

    @Autowired
    private DataSource dataSource;

    @Override
    public void afterPropertiesSet() throws Exception {
        this.instance = this;
    }

    public static Connection getConnection() throws SQLException {
        return instance.dataSource.getConnection();
    }

    public static void test() {

    }

}

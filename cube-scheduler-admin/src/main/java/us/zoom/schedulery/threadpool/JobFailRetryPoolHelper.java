package us.zoom.schedulery.threadpool;

import lombok.extern.slf4j.Slf4j;
import us.zoom.cube.scheduler.lib.model.SchedulerJobInfo;
import us.zoom.cube.scheduler.lib.model.TriggerParam;
import us.zoom.schedulery.monitors.entity.TriggerMonitor;
import us.zoom.schedulery.process.trigger.RealJobTrigger;

import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

@Slf4j
public class JobFailRetryPoolHelper {

    private static JobFailRetryPoolHelper instance = new JobFailRetryPoolHelper();

    public static JobFailRetryPoolHelper getInstance() {
        return instance;
    }

    public static final int KEEP_ALIVE_TIME = 60;

    private ThreadPoolExecutor failRetryPool = null;

    public void start() {
        failRetryPool = new ThreadPoolExecutor(
                10,
                1,
                KEEP_ALIVE_TIME,
                TimeUnit.SECONDS,
                new LinkedBlockingQueue<Runnable>(5000),
                r -> new Thread(r, "JobFailRetryPool-" + r.hashCode()),
                (r, executor) -> {
                    //todo
                    log.error(">>>>>>>>>>> JobFailRetryPool - too many jobs retry to trigger again!");
                });
    }

    public void stop() {
        failRetryPool.shutdownNow();
        log.info(">>>>>>>>> JobFailRetryPool - shutdown success. ");
    }

    public void addToFailRetry(SchedulerJobInfo jobInfo, TriggerParam triggerParam) {
        failRetryPool.execute(new Runnable() {
            @Override
            public void run() {

                int retryCount = jobInfo.getExecutorFailRetryCount();
                boolean isSuccess = false;
                for (int i = 0; i < retryCount; i++) {

                    try {
                        RealJobTrigger.runExecutor(jobInfo, triggerParam, true, new TriggerMonitor());
                        isSuccess = true;
                        break;
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                    try {
                        Thread.sleep(1000);
                    } catch (InterruptedException e) {
                    }
                }

                if(!isSuccess) {
                    //todo skip trigger for this time, add final monitor report for this trigger, and consider to add degradation and circuit breaker

                }

            }
        });
    }
}

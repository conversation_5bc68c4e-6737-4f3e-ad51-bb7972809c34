package us.zoom.schedulery.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;

@Slf4j
@Component
public class SysParamLoader {

    private static final String SYSTEM_PARAM_TYPE = "cube-scheduler-admin";

    @Scheduled(timeUnit = TimeUnit.MINUTES, fixedRate = 1)
    public void loadSysParam() {


    }

}

package us.zoom.schedulery.service;


import com.fasterxml.jackson.core.JsonProcessingException;
import lombok.extern.slf4j.Slf4j;
import us.zoom.cube.scheduler.lib.model.SchedulerJobInfo;
import us.zoom.cube.scheduler.lib.model.TriggerParam;
import us.zoom.cube.sdk.util.JsonUtils;
import us.zoom.mq.client.AsyncMQ;
import us.zoom.mq.client.DefaultAsyncMQ;
import us.zoom.mq.common.Result;
import us.zoom.mq.common.client.task.Task;
import us.zoom.mq.common.entity.TaskEntity;
import us.zoom.mq.common.listener.ProduceListener;
import us.zoom.mq.common.response.ProduceResult;

@Slf4j
public class AsyncMQInstance {

    private static final String TASK_TYPE = "cube_scheduler";

    private static AsyncMQ asyncMQ;

    public static void init(String endpoint, String username, String password) {
        if (null == asyncMQ) {
            synchronized (AsyncMQ.class) {
                if (null == asyncMQ) {
                    asyncMQ = new DefaultAsyncMQ(endpoint, username, password);
                }
            }
        }
    }

    public static void sendData(SchedulerJobInfo jobInfo, TriggerParam triggerParam) throws Exception {

        Task<String> task = new Task<>();
        task.setTopicName(jobInfo.getTriggerAsyncmqTopic());
        task.setTaskType(TASK_TYPE);
        task.setPayload(JsonUtils.toJson(triggerParam));

        Result<ProduceResult> sendResult = asyncMQ.producer().sendSync(task);
        if(!sendResult.isSuccess()) {
            throw new IllegalStateException(sendResult.getException());
        }

        //Here we temporarily use sync send so that we can complete a closed-loop for TriggerMonitor (like correct cost)

        /*asyncMQ.producer().sendAsync(task, new ProduceListener<String>() {
            @Override
            public void onSuccess(ProduceResult produceResult, TaskEntity<String> entity) {

                log.debug("job send asyncmq successfully, jobId:{}, topic:{}", jobInfo.getId(), jobInfo.getTriggerAsyncmqTopic());

                //todo to monitor the trigger result
            }

            @Override
            public void onFailed(ProduceResult produceResult, TaskEntity<String> entity, Exception e) {
                //todo to monitor the trigger result
            }
        });*/
    }


}

package us.zoom.schedulery.monitors.entity;

import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import us.zoom.cube.sdk.model.MonitorLog;

import java.util.HashMap;

@Data
public class TimeRingThreadMonitor extends AbstractBaseMonitor {


    private long cost;

    private int targetJobCount;

    private int actualJobCount;

    @Override
    public MonitorLog buildMonitorLog() {
        return MonitorLog.builder()
                //jobInScheduleMonitor.withCubeVer("1.0.0")
                .withMeasure("time_ring_thread")
                .addTag("type", StringUtils.EMPTY)
                .withNewFields(new HashMap<>(){{
                    put("cost", cost);
                    put("targetJobCount", targetJobCount);
                    put("actualJobCount", actualJobCount);
                }})
                .withTs(ts)
                .build();
    }
}

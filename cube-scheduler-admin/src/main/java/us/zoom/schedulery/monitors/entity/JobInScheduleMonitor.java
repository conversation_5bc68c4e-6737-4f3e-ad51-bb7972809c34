package us.zoom.schedulery.monitors.entity;

import lombok.Data;
import us.zoom.cube.sdk.model.MonitorLog;
import us.zoom.schedulery.monitors.enums.SchedulePhaseStatus;

import java.util.HashMap;

@Data
public class JobInScheduleMonitor extends AbstractBaseMonitor {

    private String jobId;

    private String scheduleId;

    private long nowTime;

    private long triggerLastTime;

    private long triggerNextTime;

    private SchedulePhaseStatus schedulePhase;

    private boolean refreshNextValidTimeFailed;

    private String refreshFailedReason;

    private long newTriggerNextTime;


    public MonitorLog buildMonitorLog() {
        return MonitorLog.builder()
                //.withCubeVer("1.0.0")
                .withMeasure("job_in_schedule")
                .withNewTags(new HashMap<>(){{
                    put("jobId", jobId);
                    put("schedulePhase", schedulePhase.name());
                    put("refreshNextValidTimeFailed", refreshNextValidTimeFailed);
                }})
                .withNewFields(new HashMap<>(){{
                    put("scheduleId", scheduleId);
                    put("nowTime", nowTime);
                    put("triggerLastTime", triggerLastTime);
                    put("triggerNextTime", triggerNextTime);
                    put("refreshFailedReason", refreshFailedReason);
                    put("newTriggerNextTime", newTriggerNextTime);
                }})
                .withTs(ts)
                .build();
    }

}

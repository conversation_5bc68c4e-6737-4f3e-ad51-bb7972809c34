package us.zoom.schedulery.monitors.entity;

import lombok.Data;
import us.zoom.cube.sdk.model.MonitorLog;
import us.zoom.schedulery.entity.SimpleJobInfo;

@Data
public class TriggerCreateMonitor extends AbstractBaseMonitor {

    private String scheduleId;

    private String jobId;

    private String jobName;

    public TriggerCreateMonitor() {
    }

    public TriggerCreateMonitor(SimpleJobInfo simpleJobInfo, long ts) {
        this.scheduleId = simpleJobInfo.getScheduleId();
        this.jobId = simpleJobInfo.getJobId();
        this.jobName = simpleJobInfo.getJobName();
        this.ts = ts;
    }

    @Override
    public MonitorLog buildMonitorLog() {
        return MonitorLog.builder()
                //.withCubeVer("1.0.0")
                .withMeasure("create_signal")
                .addTag("type", "trigger")
                .addTag("jobId", jobId)
                .addTag("jobName", jobName)
                .addField("scheduleId", scheduleId)
                .withTs(ts)
                .build();
    }
}

package us.zoom.schedulery.monitors;

import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import us.zoom.schedulery.monitors.entity.IBaseMonitor;
import us.zoom.schedulery.utils.JsonUtils;

import java.util.concurrent.*;

@Slf4j
public class AsyncMonitorLogger {

    private static final Logger monitorLogger = LoggerFactory.getLogger("Monitor");

    private static final Executor asyncMonitrPool;

    static {
        log.info(">>>>>>>>>>> AsyncMonitrPool - init start.");
        asyncMonitrPool = new ThreadPoolExecutor(
                1,
                10,
                60,
                TimeUnit.SECONDS,
                new LinkedBlockingQueue<Runnable>(1000),
                r -> new Thread(r, "AsyncMonitrPool-" + r.hashCode()),
                new ThreadPoolExecutor.CallerRunsPolicy());
        log.info(">>>>>>>>>>> AsyncMonitrPool - init end.");
    }

    public static void log(IBaseMonitor baseMonitor) {
        asyncMonitrPool.execute(() -> monitorLogger.info(JsonUtils.toJsonString(baseMonitor.buildMonitorLog())));
    }


}

package us.zoom.schedulery.monitors.entity;

import lombok.Data;
import us.zoom.cube.sdk.model.MonitorLog;
import us.zoom.schedulery.monitors.enums.SchedulePhaseStatus;

import java.util.HashMap;
import java.util.Map;

@Data
public class ScheduleThreadMonitor extends AbstractBaseMonitor {

    private String scheduleId;

    private int targetJobCount;

    private int actualJobCount;

    private long jobQueryCost;

    private Map<SchedulePhaseStatus, Integer> schedulePhaseCountMap;

    private int jobUpdateCount;

    private boolean scheduleFailed;

    private String failReason;

    private long cost;

    @Override
    public MonitorLog buildMonitorLog() {
        return MonitorLog.builder()
                //.withCubeVer("1.0.0")
                .withMeasure("schedule_thread")
                .addTag("scheduleFailed", scheduleFailed)
                .withNewFields(new HashMap<>(){{
                    put("scheduleId", scheduleId);
                    put("targetJobCount", targetJobCount);
                    put("actualJobCount", actualJobCount);
                    put("jobQueryCost", jobQueryCost);
                    //todo put("schedulePhaseCountMap", schedulePhaseCountMap);
                    put("jobUpdateCount", jobUpdateCount);
                    put("failReason", failReason);
                    put("cost", cost);
                }})
                .withTs(ts)
                .build();
    }
}

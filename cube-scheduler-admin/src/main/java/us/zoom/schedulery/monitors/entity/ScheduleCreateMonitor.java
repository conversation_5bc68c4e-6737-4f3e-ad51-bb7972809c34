package us.zoom.schedulery.monitors.entity;

import lombok.Data;
import us.zoom.cube.sdk.model.MonitorLog;

@Data
public class ScheduleCreateMonitor extends AbstractBaseMonitor {

    private String scheduleId;

    public ScheduleCreateMonitor() {
    }

    public ScheduleCreateMonitor(String scheduleId, long ts) {
        this.scheduleId = scheduleId;
        this.ts = ts;
    }

    @Override
    public MonitorLog buildMonitorLog() {
        return MonitorLog.builder()
                //.withCubeVer("1.0.0")
                .withMeasure("create_signal")
                .addTag("type", "schedule")
                .addField("scheduleId", scheduleId)
                .withTs(ts)
                .build();
    }
}

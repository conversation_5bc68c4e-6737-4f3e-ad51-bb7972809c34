package us.zoom.schedulery.monitors.entity;

import lombok.Data;
import us.zoom.cube.sdk.model.MonitorLog;
import us.zoom.schedulery.enums.TriggerSourceEnum;

import java.util.HashMap;

@Data
public class TriggerThreadMonitor extends AbstractBaseMonitor {

    private String triggerId;

    private String scheduleId;

    private String jobId;

    private String jobName;

    private TriggerSourceEnum triggerSource;

    private long cost;


    @Override
    public MonitorLog buildMonitorLog() {
        return MonitorLog.builder()
                //.withCubeVer("1.0.0")
                .withMeasure("trigger_thread")
                .withNewTags(new HashMap<>(){{
                    put("jobId", jobId);
                    put("jobName", jobName);
                    put("triggerSource", triggerSource.name());
                }})
                .withNewFields(new HashMap<>(){{
                    put("triggerId", triggerId);
                    put("scheduleId", scheduleId);
                    put("cost", cost);
                }})
                .withTs(ts)
                .build();
    }
}

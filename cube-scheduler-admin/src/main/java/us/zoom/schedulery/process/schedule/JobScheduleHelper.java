package us.zoom.schedulery.process.schedule;


import lombok.extern.slf4j.Slf4j;
import us.zoom.cube.scheduler.lib.constants.ScheduleConstant;
import us.zoom.cube.scheduler.lib.enums.MisfireStrategy;
import us.zoom.cube.scheduler.lib.model.SchedulerJobInfo;
import us.zoom.cube.scheduler.lib.utils.ScheduleUtil;
import us.zoom.schedulery.config.SchedulerConfig;
import us.zoom.schedulery.dao.SchedulerJobInfoDao;
import us.zoom.schedulery.entity.SimpleJobInfo;
import us.zoom.schedulery.enums.TriggerTypeEnum;
import us.zoom.schedulery.monitors.AsyncMonitorLogger;
import us.zoom.schedulery.monitors.entity.JobInScheduleMonitor;
import us.zoom.schedulery.monitors.entity.ScheduleCreateMonitor;
import us.zoom.schedulery.monitors.entity.ScheduleThreadMonitor;
import us.zoom.schedulery.monitors.entity.TimeRingThreadMonitor;
import us.zoom.schedulery.monitors.enums.SchedulePhaseStatus;
import us.zoom.schedulery.threadpool.JobTriggerPoolHelper;
import us.zoom.schedulery.utils.ApplicationContextHolder;
import us.zoom.schedulery.utils.DataSourceHelper;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR> 2019-05-21
 */
@Slf4j
public class JobScheduleHelper {

    private static JobScheduleHelper instance = new JobScheduleHelper();

    public static JobScheduleHelper getInstance() {
        return instance;
    }


    private Thread scheduleThread;
    private Thread ringThread;
    private volatile boolean scheduleThreadToStop = false;
    private volatile boolean ringThreadToStop = false;
    //private volatile static Map<Integer, List<String>> ringData = new ConcurrentHashMap<>();
    private volatile static Map<Integer, List<SimpleJobInfo>> ringData = new ConcurrentHashMap<>();

    public void start() {

        SchedulerJobInfoDao schedulerJobInfoDao = ApplicationContextHolder.getBean(SchedulerJobInfoDao.class);

        // schedule thread
        scheduleThread = new Thread(new Runnable() {
            @Override
            public void run() {

                try {
                    /**
                     * The purpose is to:
                     * 1. Ensure the thread wakes up at the beginning of a second
                     * 2. Add an initial 5-second delay before starting the schedule thread's main work
                     * For example:
                     * - If the current time is 12:00:00.800 (800ms into the current second)
                     * - The calculation would be: 5000 - 800 = 4200
                     * - The thread will sleep for 4200ms, waking up exactly at 12:00:05.000 (the start of a second)
                     */
                    TimeUnit.MILLISECONDS.sleep(5000 - System.currentTimeMillis() % 1000);
                } catch (Throwable e) {
                    if (!scheduleThreadToStop) {
                        log.error(e.getMessage(), e);
                    }
                }
                log.info(">>>>>>>>> JobScheduleHelper init scheduler-admin success.");

                // pre_read count: threadpool_size * trigger_qps (each trigger cost 50ms, qps = 1000/50 = 20) //todo fjdingsd: so this qps can be set as parameter and we can monitor trigger cost to change anytime
                int preReadCount = (SchedulerConfig.getConfig().getTriggerPoolFastMax() + SchedulerConfig.getConfig().getTriggerPoolSlowMax()) * 20;

                while (!scheduleThreadToStop) {

                    // Scan Job
                    long start = System.currentTimeMillis();
                    final String scheduleId = UUID.randomUUID().toString();
                    AsyncMonitorLogger.log(new ScheduleCreateMonitor(scheduleId, start));
                    ScheduleThreadMonitor scheduleThreadMonitor = buildScheduleThreadMonitor(scheduleId, preReadCount, start);

                    int updateCount = 0;

                    Connection conn = null;
                    Boolean connAutoCommit = null;
                    PreparedStatement preparedStatement = null;

                    boolean preReadSuc = true;
                    try {
                        conn = DataSourceHelper.getConnection();
                        connAutoCommit = conn.getAutoCommit();
                        conn.setAutoCommit(false);

                        //todo fjdingsd: to adjust the lock name
                        preparedStatement = conn.prepareStatement("select * from cube_scheduler_lock where lock_name = 'schedule_lock' for update");
                        preparedStatement.execute();

                        // tx start

                        // 1、pre read
                        long nowTime = System.currentTimeMillis();
                        List<SchedulerJobInfo> scheduleList = schedulerJobInfoDao.scheduleJobQuery(nowTime + ScheduleConstant.PRE_READ_MS, preReadCount);

                        scheduleThreadMonitor.setJobQueryCost(System.currentTimeMillis() - start);
                        scheduleThreadMonitor.setActualJobCount(scheduleList == null ? 0 : scheduleList.size());

                        if (scheduleList != null && scheduleList.size() > 0) {
                            // 2、push time-ring
                            for (SchedulerJobInfo jobInfo : scheduleList) {
                                SimpleJobInfo simpleJobInfo = SimpleJobInfo.create(jobInfo, scheduleId);
                                JobInScheduleMonitor jobInScheduleMonitor = buildJobInScheduleMonitor(jobInfo, nowTime, scheduleId);

                                try {

                                    // time-ring jump
                                    if (nowTime > jobInfo.getTriggerNextTime() + ScheduleConstant.PRE_READ_MS) {
                                        // 2.1、trigger-expire > 5s：pass && make next-trigger-time
                                        log.warn(">>>>>>>>>>> JobScheduleHelper schedule misfire, jobId = " + jobInfo.getId());


                                        // 1、misfire match
                                        //MisfireStrategy misfireStrategyEnum = MisfireStrategy.match(jobInfo.getMisfireStrategy(), MisfireStrategy.SKIP);
                                        MisfireStrategy misfireStrategyEnum = jobInfo.getMisfireStrategy();
                                        if (MisfireStrategy.EXECUTE == misfireStrategyEnum) {
                                            // FIRE_ONCE_NOW 》 trigger
                                            JobTriggerPoolHelper.trigger(SimpleJobInfo.create(jobInfo, scheduleId), TriggerTypeEnum.MISFIRE, -1, null, null, null);
                                            log.debug(">>>>>>>>>>> JobScheduleHelper schedule push trigger : jobId = " + jobInfo.getId());

                                            jobInScheduleMonitor.setSchedulePhase(SchedulePhaseStatus.MISFIRE_AND_EXECUTE);
                                        } else {
                                            jobInScheduleMonitor.setSchedulePhase(SchedulePhaseStatus.MISFIRE);
                                        }

                                        // 2、fresh next
                                        refreshNextValidTime(jobInfo, new Date(), jobInScheduleMonitor);

                                    } else if (nowTime > jobInfo.getTriggerNextTime()) {
                                        // 2.2、trigger-expire < 5s：direct-trigger && make next-trigger-time

                                        // 1、trigger
                                        JobTriggerPoolHelper.trigger(SimpleJobInfo.create(jobInfo, scheduleId), TriggerTypeEnum.CRON, -1, null, null, null);
                                        log.debug(">>>>>>>>>>> JobScheduleHelper, schedule push trigger : jobId = " + jobInfo.getId());

                                        // 2、fresh next
                                        refreshNextValidTime(jobInfo, new Date(), jobInScheduleMonitor);

                                        // next-trigger-time in 5s, pre-read again
                                        if (jobInfo.isEnabled() && nowTime + ScheduleConstant.PRE_READ_MS > jobInfo.getTriggerNextTime()) {

                                            // 1、make ring second
                                            int ringSecond = (int) ((jobInfo.getTriggerNextTime() / 1000) % 60);

                                            // 2、push time ring
                                            pushTimeRing(ringSecond, jobInfo, scheduleId);

                                            // 3、fresh next
                                            refreshNextValidTime(jobInfo, new Date(jobInfo.getTriggerNextTime()), jobInScheduleMonitor);

                                            jobInScheduleMonitor.setSchedulePhase(SchedulePhaseStatus.FIRE_AND_TIMERING);

                                        } else {
                                            jobInScheduleMonitor.setSchedulePhase(SchedulePhaseStatus.FIRE);
                                        }

                                    } else {
                                        // 2.3、trigger-pre-read：time-ring trigger && make next-trigger-time

                                        // 1、make ring second
                                        int ringSecond = (int) ((jobInfo.getTriggerNextTime() / 1000) % 60);

                                        // 2、push time ring
                                        pushTimeRing(ringSecond, jobInfo, scheduleId);

                                        // 3、fresh next
                                        refreshNextValidTime(jobInfo, new Date(jobInfo.getTriggerNextTime()), jobInScheduleMonitor);

                                        jobInScheduleMonitor.setSchedulePhase(SchedulePhaseStatus.TIMERING);

                                    }

                                } finally {
                                    AsyncMonitorLogger.log(jobInScheduleMonitor);
                                }

                            }

                            // 3、update trigger info
                            for (SchedulerJobInfo jobInfo : scheduleList) {
                                schedulerJobInfoDao.updateEnableAndTriggerTime(jobInfo);
                                updateCount++;
                            }
                            //todo fjdingsd: to monitor how many jobs have been updte the status

                        } else {
                            preReadSuc = false;
                        }

                        // tx stop


                    } catch (Throwable e) {
                        if (!scheduleThreadToStop) {
                            log.error(">>>>>>>>>>> JobScheduleHelper#scheduleThread error:{}", e);
                            scheduleThreadMonitor.setScheduleFailed(true);
                            scheduleThreadMonitor.setFailReason(e.getMessage());
                        }
                    } finally {

                        // commit
                        if (conn != null) {
                            try {
                                conn.commit();
                            } catch (Throwable e) {
                                if (!scheduleThreadToStop) {
                                    log.error(e.getMessage(), e);
                                    scheduleThreadMonitor.setScheduleFailed(true);
                                    scheduleThreadMonitor.setFailReason(e.getMessage());
                                }
                            }
                            try {
                                conn.setAutoCommit(connAutoCommit);
                            } catch (Throwable e) {
                                if (!scheduleThreadToStop) {
                                    log.error(e.getMessage(), e);
                                    scheduleThreadMonitor.setScheduleFailed(true);
                                    scheduleThreadMonitor.setFailReason(e.getMessage());
                                }
                            }
                            try {
                                conn.close();
                            } catch (Throwable e) {
                                if (!scheduleThreadToStop) {
                                    log.error(e.getMessage(), e);
                                    scheduleThreadMonitor.setScheduleFailed(true);
                                    scheduleThreadMonitor.setFailReason(e.getMessage());
                                }
                            }
                        }

                        // close PreparedStatement
                        if (null != preparedStatement) {
                            try {
                                preparedStatement.close();
                            } catch (Throwable e) {
                                if (!scheduleThreadToStop) {
                                    log.error(e.getMessage(), e);
                                    scheduleThreadMonitor.setScheduleFailed(true);
                                    scheduleThreadMonitor.setFailReason(e.getMessage());
                                }
                            }
                        }
                    }

                    scheduleThreadMonitor.setJobUpdateCount(updateCount);
                    scheduleThreadMonitor.setCost(System.currentTimeMillis() - start);
                    AsyncMonitorLogger.log(scheduleThreadMonitor);


                    long cost = System.currentTimeMillis() - start;

                    // Wait seconds, align second
                    if (cost < 1000) {  // scan-overtime, not wait
                        try {
                            // pre-read period: success > scan each second; fail > skip this period;
                            TimeUnit.MILLISECONDS.sleep((preReadSuc ? 1000 : ScheduleConstant.PRE_READ_MS) - System.currentTimeMillis() % 1000);
                        } catch (Throwable e) {
                            if (!scheduleThreadToStop) {
                                log.error(e.getMessage(), e);
                            }
                        }
                    }

                }

                log.info(">>>>>>>>>>> JobScheduleHelper#scheduleThread stop");
            }
        });
        scheduleThread.setDaemon(true);
        scheduleThread.setName("scheduleThread");
        scheduleThread.start();


        // ring thread
        ringThread = new Thread(new Runnable() {
            @Override
            public void run() {

                while (!ringThreadToStop) {

                    // align second
                    try {
                        TimeUnit.MILLISECONDS.sleep(1000 - System.currentTimeMillis() % 1000);
                    } catch (Throwable e) {
                        if (!ringThreadToStop) {
                            log.error(e.getMessage(), e);
                        }
                    }

                    TimeRingThreadMonitor timeRingThreadMonitor = new TimeRingThreadMonitor();
                    timeRingThreadMonitor.setTs(System.currentTimeMillis());
                    int triggerCount = 0;

                    try {
                        // second data
                        //List<String> ringItemData = new ArrayList<>();
                        List<SimpleJobInfo> ringItemData = new ArrayList<>();
                        // To prevent task loss when processing time exceeds the time unit (tick), verify the previous time unit as well
                        int nowSecond = Calendar.getInstance().get(Calendar.SECOND);
                        for (int i = 0; i < 2; i++) {
                            //List<String> tmpData = ringData.remove((nowSecond + 60 - i) % 60);
                            List<SimpleJobInfo> tmpData = ringData.remove((nowSecond + 60 - i) % 60);
                            if (tmpData != null) {
                                ringItemData.addAll(tmpData);
                            }
                        }
                        timeRingThreadMonitor.setTargetJobCount(ringItemData.size());

                        // ring trigger
                        log.debug(">>>>>>>>>>> JobScheduleHelper time-ring beat : " + nowSecond + " = " + Arrays.asList(ringItemData));
                        if (ringItemData.size() > 0) {
                            // do trigger
                            //for (String jobId : ringItemData) {
                            for (SimpleJobInfo simpleJobInfo : ringItemData) {
                                // do trigger
                                JobTriggerPoolHelper.trigger(simpleJobInfo, TriggerTypeEnum.CRON, -1, null, null, null);
                                triggerCount++;
                            }
                            // clear
                            ringItemData.clear();
                        }
                    } catch (Throwable e) {
                        if (!ringThreadToStop) {
                            log.error(">>>>>>>>>>> JobScheduleHelper JobScheduleHelper#ringThread error:{}", e);
                        }
                    } finally {
                        timeRingThreadMonitor.setActualJobCount(triggerCount);
                        timeRingThreadMonitor.setCost(System.currentTimeMillis() - timeRingThreadMonitor.getTs());
                        AsyncMonitorLogger.log(timeRingThreadMonitor);
                    }
                }
                log.info(">>>>>>>>>>> JobScheduleHelper#ringThread stop");
            }
        });
        ringThread.setDaemon(true);
        ringThread.setName("JobScheduleHelper#ringThread");
        ringThread.start();
    }

    private ScheduleThreadMonitor buildScheduleThreadMonitor(String scheduleId, int preReadCount, long start) {
        ScheduleThreadMonitor scheduleThreadMonitor = new ScheduleThreadMonitor();
        scheduleThreadMonitor.setTs(start);
        scheduleThreadMonitor.setScheduleId(scheduleId);
        scheduleThreadMonitor.setTargetJobCount(preReadCount);
        return scheduleThreadMonitor;
    }

    private void refreshNextValidTime(SchedulerJobInfo jobInfo, Date fromTime, JobInScheduleMonitor jobInScheduleMonitor) {
        try {
            Date nextValidTime = ScheduleUtil.generateNextValidTime(jobInfo.getScheduleType(), jobInfo.getScheduleConf(), fromTime);
            if (nextValidTime != null) {
                //jobInfo.setTriggerStatus(-1);                               // pass, may be Inaccurate
                jobInfo.setEnabled(true);
                jobInfo.setTriggerLastTime(jobInfo.getTriggerNextTime());
                jobInfo.setTriggerNextTime(nextValidTime.getTime());
            } else {
                // generateNextValidTime fail, stop job
                //jobInfo.setTriggerStatus(0);
                jobInfo.setEnabled(false);
                jobInfo.setTriggerLastTime(0);
                jobInfo.setTriggerNextTime(0);
                log.error(">>>>>>>>>>> JobScheduleHelper refreshNextValidTime fail for job: jobId={}, scheduleType={}, scheduleConf={}",
                        jobInfo.getId(), jobInfo.getScheduleType(), jobInfo.getScheduleConf());
                //todo fjdingsd: to monitor and alert this situation

                jobInScheduleMonitor.setRefreshNextValidTimeFailed(true);
                jobInScheduleMonitor.setRefreshFailedReason("Invalid or Unsupported Schedule Type: " + jobInfo.getScheduleType() + ", Schedule Conf: " + jobInfo.getScheduleConf());

            }
        } catch (Throwable e) {
            // generateNextValidTime error, stop job
            //jobInfo.setTriggerStatus(0);
            jobInfo.setEnabled(false);
            jobInfo.setTriggerLastTime(0);
            jobInfo.setTriggerNextTime(0);

            log.error(">>>>>>>>>>> JobScheduleHelper refreshNextValidTime error for job: jobId={}, scheduleType={}, scheduleConf={}",
                    jobInfo.getId(), jobInfo.getScheduleType(), jobInfo.getScheduleConf(), e);
            //todo fjdingsd: to monitor and alert this situation
            jobInScheduleMonitor.setRefreshNextValidTimeFailed(true);
            jobInScheduleMonitor.setRefreshFailedReason(e.getMessage());

        } finally {
            jobInScheduleMonitor.setNewTriggerNextTime(jobInfo.getTriggerNextTime());
        }
    }

    private void pushTimeRing(int ringSecond, SchedulerJobInfo jobInfo, String scheduleId) {
        // push async ring
        //List<String> ringItemData = ringData.get(ringSecond);
        List<SimpleJobInfo> ringItemData = ringData.get(ringSecond);
        if (ringItemData == null) {
            ringItemData = new ArrayList<>();
            ringData.put(ringSecond, ringItemData);
        }
        //ringItemData.add(jobId);
        ringItemData.add(SimpleJobInfo.create(jobInfo, scheduleId));

        log.debug(">>>>>>>>>>> JobScheduleHelper schedule push time-ring : " + ringSecond + " = " + Arrays.asList(ringItemData));
    }

    public void toStop() {

        // 1、stop schedule
        scheduleThreadToStop = true;
        try {
            TimeUnit.SECONDS.sleep(1);  // wait
        } catch (Throwable e) {
            log.error(e.getMessage(), e);
        }
        if (scheduleThread.getState() != Thread.State.TERMINATED) {
            // interrupt and wait
            scheduleThread.interrupt();
            try {
                scheduleThread.join();
            } catch (Throwable e) {
                log.error(e.getMessage(), e);
            }
        }

        // if has ring data
        boolean hasRingData = false;
        if (!ringData.isEmpty()) {
            for (int second : ringData.keySet()) {
                //List<String> tmpData = ringData.get(second);
                List<SimpleJobInfo> tmpData = ringData.get(second);
                if (tmpData != null && tmpData.size() > 0) {
                    hasRingData = true;
                    break;
                }
            }
        }
        if (hasRingData) {
            try {
                TimeUnit.SECONDS.sleep(8);
            } catch (Throwable e) {
                log.error(e.getMessage(), e);
            }
        }

        // stop ring (wait job-in-memory stop)
        ringThreadToStop = true;
        try {
            TimeUnit.SECONDS.sleep(1);
        } catch (Throwable e) {
            log.error(e.getMessage(), e);
        }
        if (ringThread.getState() != Thread.State.TERMINATED) {
            // interrupt and wait
            ringThread.interrupt();
            try {
                ringThread.join();
            } catch (Throwable e) {
                log.error(e.getMessage(), e);
            }
        }

        log.info(">>>>>>>>>>> JobScheduleHelper stop");
    }

    private JobInScheduleMonitor buildJobInScheduleMonitor(SchedulerJobInfo jobInfo, long nowTime, String scheduleId) {
        JobInScheduleMonitor jobInScheduleMonitor = new JobInScheduleMonitor();
        jobInScheduleMonitor.setScheduleId(scheduleId);
        jobInScheduleMonitor.setJobId(jobInfo.getId());
        jobInScheduleMonitor.setTriggerLastTime(jobInfo.getTriggerLastTime());
        jobInScheduleMonitor.setTriggerNextTime(jobInfo.getTriggerNextTime());
        jobInScheduleMonitor.setNowTime(nowTime);
        return jobInScheduleMonitor;
    }

    // ---------------------- tools ----------------------
    /*public static Date generateNextValidTime(SchedulerJobInfo jobInfo, Date fromTime) throws Exception {
        //ScheduleType scheduleTypeEnum = ScheduleType.match(jobInfo.getScheduleType(), null);
        ScheduleType scheduleTypeEnum = jobInfo.getScheduleType();
        if (ScheduleType.CRON == scheduleTypeEnum) {
            Date nextValidTime = new CronExpression(jobInfo.getScheduleConf()).getNextValidTimeAfter(fromTime);
            return nextValidTime;
        } else if (ScheduleType.FIXED_RATE == scheduleTypeEnum *//*|| ScheduleTypeEnum.FIX_DELAY == scheduleTypeEnum*//*) {
            return new Date(fromTime.getTime() + Integer.valueOf(jobInfo.getScheduleConf()) * 1000);
        }
        return null;
    }*/

}

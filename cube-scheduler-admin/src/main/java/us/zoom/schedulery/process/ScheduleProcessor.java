package us.zoom.schedulery.process;

import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.DependsOn;
import org.springframework.stereotype.Component;
import us.zoom.schedulery.config.SchedulerConfig;
import us.zoom.schedulery.process.schedule.JobScheduleHelper;
import us.zoom.schedulery.threadpool.JobTriggerPoolHelper;

/**
 * @author: <PERSON><PERSON> Ding
 * @date: 2025/3/26 17:32
 * @desc:
 */
@Component
@DependsOn("applicationContextHolder")
public class ScheduleProcessor implements InitializingBean {

    @Autowired
    private SchedulerConfig config;


    @Override
    public void afterPropertiesSet() throws Exception {
        init();
    }

    public void init() {
        //start trigger pool
        JobTriggerPoolHelper.toStart();

        //start registry monitor


        //fail-monitor run (optional)


        //start callback pool

        //start schedule pool (must be last)
        JobScheduleHelper.getInstance().start();
    }

    public void destroy() {

    }


}

package us.zoom.schedulery.process.trigger;


import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.Assert;
import us.zoom.cube.scheduler.lib.enums.ChannelMedia;
import us.zoom.cube.scheduler.lib.model.SchedulerJobInfo;
import us.zoom.cube.scheduler.lib.model.TriggerParam;
import us.zoom.schedulery.dao.SchedulerJobInfoDao;
import us.zoom.schedulery.entity.SimpleJobInfo;
import us.zoom.schedulery.enums.TriggerTypeEnum;
import us.zoom.schedulery.monitors.entity.TriggerMonitor;
import us.zoom.schedulery.service.AsyncMQInstance;
import us.zoom.schedulery.utils.ApplicationContextHolder;

@Slf4j
public class RealJobTrigger {

    public static final String JOB_NOT_FOUND_MESSAGE = "Job Not Found!";

    public static void trigger(SimpleJobInfo simpleJobInfo,
                               TriggerTypeEnum triggerType,
                               int failRetryCount,
                               String executorParam,
                               String addressList,
                               TriggerMonitor triggerMonitor) {


        // load data
        final String jobId = simpleJobInfo.getJobId();
        SchedulerJobInfoDao schedulerJobInfoDao = ApplicationContextHolder.getBean(SchedulerJobInfoDao.class);
        SchedulerJobInfo jobInfo = schedulerJobInfoDao.getById(jobId);

        if (jobInfo == null) {
            //todo to add monitoring (maybe deleted during schedule)
            log.warn(">>>>>>>>>>>> RealJobTrigger trigger fail, jobId invalid，jobId={}", jobId);
            triggerMonitor.setTriggerFailed(true);
            triggerMonitor.setFailReason(JOB_NOT_FOUND_MESSAGE);
            return;
        }
        if (executorParam != null) {
            //todo variables replace

            jobInfo.setJobParam(executorParam);
        }
        int finalFailRetryCount = failRetryCount >= 0 ? failRetryCount : jobInfo.getExecutorFailRetryCount();


        processTrigger(jobInfo, finalFailRetryCount, triggerType, triggerMonitor, simpleJobInfo);


    }

    private static boolean isNumeric(String str) {
        try {
            int result = Integer.valueOf(str);
            return true;
        } catch (NumberFormatException e) {
            return false;
        }
    }


    private static void processTrigger(SchedulerJobInfo jobInfo, int finalFailRetryCount, TriggerTypeEnum triggerType, TriggerMonitor triggerMonitor, SimpleJobInfo simpleJobInfo) {
        log.debug(">>>>>>>>>>> RealJobTrigger trigger start, jobId:{}", jobInfo.getId());

        // 2、init trigger-param
        TriggerParam triggerParam = new TriggerParam();
        triggerParam.setJobId(jobInfo.getId());
        triggerParam.setName(jobInfo.getName());
        triggerParam.setJobParam(jobInfo.getJobParam());

        long triggerNextTime = simpleJobInfo.getTriggerNextTime();
        long triggerLastTime = simpleJobInfo.getTriggerLastTime();

        if (jobInfo.isTimeRangeAlign()) {
            triggerNextTime = triggerNextTime / (1000 * 60) * (1000 * 60);
            triggerLastTime = triggerLastTime / (1000 * 60) * (1000 * 60);
        }
        if (jobInfo.isTurnBackOffsetEnable()) {
            triggerNextTime = triggerNextTime - jobInfo.getTriggerInitOffsetSeconds() * 1000;
            triggerLastTime = triggerLastTime - jobInfo.getTriggerInitOffsetSeconds() * 1000;
        }
        //to prevent meaningless time value caused by turning back, this is mainly used for first trigger
        if (triggerLastTime < 0) {
            triggerLastTime = 0;
        }
        triggerParam.setTriggerTime(triggerNextTime);
        triggerParam.setTriggerLastTime(triggerLastTime);


        // 4、trigger remote executor
        runExecutor(jobInfo, triggerParam, false, triggerMonitor);

        // 5、collection trigger info

        // 6、save log trigger-info
        //XxlJobAdminConfig.getAdminConfig().getXxlJobLogDao().updateTriggerInfo(jobLog);

        log.debug(">>>>>>>>>>> RealJobTrigger trigger end, jobId:{}", jobInfo.getId());
    }

    public static void runExecutor(SchedulerJobInfo jobInfo, TriggerParam triggerParam, boolean fromFailedRetry, TriggerMonitor triggerMonitor) {
        try {
            // validate jobInfo finally again
            ChannelMedia channelMedia = jobInfo.getChannelMedia();
            triggerMonitor.setChannelMedia(channelMedia);

            if (ChannelMedia.AsyncMQ == channelMedia) {
                Assert.isTrue(StringUtils.isNotBlank(jobInfo.getTriggerAsyncmqTopic()), "triggerAsyncmqTopic is blank");
                AsyncMQInstance.sendData(jobInfo, triggerParam);

            } else if (ChannelMedia.HTTP == channelMedia) {
                //todo
            } else if (ChannelMedia.RPC == channelMedia) {
                //todo
            } else {
                //todo
            }
        } catch (Exception e) {
            log.error(">>>>>>>>>>> RealJobTrigger trigger fail, jobId:{}", jobInfo.getId(), e);
            //todo
            triggerMonitor.setTriggerFailed(true);
            triggerMonitor.setFailReason(e.getMessage());

            if (!fromFailedRetry) {
                int executorFailRetryCount = jobInfo.getExecutorFailRetryCount();
                //todo
                /*if(executorFailRetryCount > 0) {
                    triggerMonitor.setFailRetry(true);
                    JobFailRetryPoolHelper.getInstance().addToFailRetry(jobInfo, triggerParam);
                }*/
            }
        }

    }

}

package us.zoom.schedulery.entity;

import lombok.Data;
import us.zoom.cube.scheduler.lib.model.SchedulerJobInfo;

@Data
public class SimpleJobInfo {

    private String jobId;

    private String jobName;

    private String scheduleId;

    private String tenantId;

    private long triggerLastTime;
    private long triggerNextTime;

    public SimpleJobInfo() {
    }

    public static SimpleJobInfo create(SchedulerJobInfo jobInfo, String scheduleId) {
        SimpleJobInfo simpleJobInfo = new SimpleJobInfo();
        simpleJobInfo.setJobId(jobInfo.getId());
        simpleJobInfo.setJobName(jobInfo.getName());
        simpleJobInfo.setScheduleId(scheduleId);
        simpleJobInfo.setTenantId(jobInfo.getTenantId());
        simpleJobInfo.setTriggerLastTime(jobInfo.getTriggerLastTime());
        simpleJobInfo.setTriggerNextTime(jobInfo.getTriggerNextTime());

        return simpleJobInfo;
    }
}

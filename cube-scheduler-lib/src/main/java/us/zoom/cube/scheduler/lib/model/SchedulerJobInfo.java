package us.zoom.cube.scheduler.lib.model;


import us.zoom.cube.scheduler.lib.enums.*;

import java.util.Date;

/**
 * @author: <PERSON><PERSON> Ding
 * @date: 2025/3/26 15:43
 * @desc:
 */
public class SchedulerJobInfo {

    private String id;
    private String name;
    private String jobDesc;
    private String tenantId;
    private String refId;
    private String busiType;
    private ScheduleType scheduleType;
    private String scheduleConf;
    private boolean enabled;
    private String jobParam;
    private TriggerStatus triggerStatus;
    private ExecutorType executorType;
    private ChannelMedia channelMedia;
    private String executorInfoId;  //can't be null if ExexutorTypoe is callback
    private MisfireStrategy misfireStrategy;
    private boolean timeRangeEnabled;
    private boolean timeRangeAlign;
    private int triggerTimeoutRetryCount;
    private String triggerAsyncmqTopic;  //can't be null if ChannelMedia is AsyncMQ
    private String triggerExecutorUrl; //can't be null if ChannelMedia is HTTP
    private String csmsApp;

    private ExecutorBlockStrategy executorBlockStrategy; //only for callback
    private int executorTimeout; //only for callback
    private int executorFailRetryCount; //only for callback

    private TriggerRouteStrategy triggerRouteStrategy;

    private long triggerInitOffsetSeconds;
    private boolean turnBackOffsetEnable;

    private long triggerLastTime;
    private long triggerNextTime;

    private Date createTime;
    private Date updateTime;
    private String creator;
    private String editor;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getJobDesc() {
        return jobDesc;
    }

    public void setJobDesc(String jobDesc) {
        this.jobDesc = jobDesc;
    }

    public String getTenantId() {
        return tenantId;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    public String getRefId() {
        return refId;
    }

    public void setRefId(String refId) {
        this.refId = refId;
    }

    public String getBusiType() {
        return busiType;
    }

    public void setBusiType(String busiType) {
        this.busiType = busiType;
    }

    public ScheduleType getScheduleType() {
        return scheduleType;
    }

    public void setScheduleType(ScheduleType scheduleType) {
        this.scheduleType = scheduleType;
    }

    public String getScheduleConf() {
        return scheduleConf;
    }

    public void setScheduleConf(String scheduleConf) {
        this.scheduleConf = scheduleConf;
    }

    public boolean isEnabled() {
        return enabled;
    }

    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
    }

    public String getJobParam() {
        return jobParam;
    }

    public void setJobParam(String jobParam) {
        this.jobParam = jobParam;
    }

    public TriggerStatus getTriggerStatus() {
        return triggerStatus;
    }

    public void setTriggerStatus(TriggerStatus triggerStatus) {
        this.triggerStatus = triggerStatus;
    }

    public ExecutorType getExecutorType() {
        return executorType;
    }

    public void setExecutorType(ExecutorType executorType) {
        this.executorType = executorType;
    }

    public ChannelMedia getChannelMedia() {
        return channelMedia;
    }

    public void setChannelMedia(ChannelMedia channelMedia) {
        this.channelMedia = channelMedia;
    }

    public String getExecutorInfoId() {
        return executorInfoId;
    }

    public void setExecutorInfoId(String executorInfoId) {
        this.executorInfoId = executorInfoId;
    }

    public MisfireStrategy getMisfireStrategy() {
        return misfireStrategy;
    }

    public void setMisfireStrategy(MisfireStrategy misfireStrategy) {
        this.misfireStrategy = misfireStrategy;
    }

    public boolean isTimeRangeEnabled() {
        return timeRangeEnabled;
    }

    public void setTimeRangeEnabled(boolean timeRangeEnabled) {
        this.timeRangeEnabled = timeRangeEnabled;
    }

    public boolean isTimeRangeAlign() {
        return timeRangeAlign;
    }

    public void setTimeRangeAlign(boolean timeRangeAlign) {
        this.timeRangeAlign = timeRangeAlign;
    }

    public int getTriggerTimeoutRetryCount() {
        return triggerTimeoutRetryCount;
    }

    public void setTriggerTimeoutRetryCount(int triggerTimeoutRetryCount) {
        this.triggerTimeoutRetryCount = triggerTimeoutRetryCount;
    }

    public String getTriggerAsyncmqTopic() {
        return triggerAsyncmqTopic;
    }

    public void setTriggerAsyncmqTopic(String triggerAsyncmqTopic) {
        this.triggerAsyncmqTopic = triggerAsyncmqTopic;
    }

    public String getTriggerExecutorUrl() {
        return triggerExecutorUrl;
    }

    public void setTriggerExecutorUrl(String triggerExecutorUrl) {
        this.triggerExecutorUrl = triggerExecutorUrl;
    }

    public String getCsmsApp() {
        return csmsApp;
    }

    public void setCsmsApp(String csmsApp) {
        this.csmsApp = csmsApp;
    }

    public ExecutorBlockStrategy getExecutorBlockStrategy() {
        return executorBlockStrategy;
    }

    public void setExecutorBlockStrategy(ExecutorBlockStrategy executorBlockStrategy) {
        this.executorBlockStrategy = executorBlockStrategy;
    }

    public int getExecutorTimeout() {
        return executorTimeout;
    }

    public void setExecutorTimeout(int executorTimeout) {
        this.executorTimeout = executorTimeout;
    }

    public int getExecutorFailRetryCount() {
        return executorFailRetryCount;
    }

    public void setExecutorFailRetryCount(int executorFailRetryCount) {
        this.executorFailRetryCount = executorFailRetryCount;
    }

    public TriggerRouteStrategy getTriggerRouteStrategy() {
        return triggerRouteStrategy;
    }

    public void setTriggerRouteStrategy(TriggerRouteStrategy triggerRouteStrategy) {
        this.triggerRouteStrategy = triggerRouteStrategy;
    }

    public long getTriggerInitOffsetSeconds() {
        return triggerInitOffsetSeconds;
    }

    public void setTriggerInitOffsetSeconds(long triggerInitOffsetSeconds) {
        this.triggerInitOffsetSeconds = triggerInitOffsetSeconds;
    }

    public boolean isTurnBackOffsetEnable() {
        return turnBackOffsetEnable;
    }

    public void setTurnBackOffsetEnable(boolean turnBackOffsetEnable) {
        this.turnBackOffsetEnable = turnBackOffsetEnable;
    }

    public long getTriggerLastTime() {
        return triggerLastTime;
    }

    public void setTriggerLastTime(long triggerLastTime) {
        this.triggerLastTime = triggerLastTime;
    }

    public long getTriggerNextTime() {
        return triggerNextTime;
    }

    public void setTriggerNextTime(long triggerNextTime) {
        this.triggerNextTime = triggerNextTime;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getCreator() {
        return creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public String getEditor() {
        return editor;
    }

    public void setEditor(String editor) {
        this.editor = editor;
    }
}

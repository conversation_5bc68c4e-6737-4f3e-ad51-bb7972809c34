package us.zoom.cube.scheduler.lib.model;

import java.util.Date;

/**
 * @author: <PERSON><PERSON> Ding
 * @date: 2025/3/26 15:42
 * @desc:
 */
public class SchedulerLock {

    private String cubeService;

    private String lockExecutorName;

    private String executorUnit;

    private Date createTime;

    private Date updateTime;

    public String getCubeService() {
        return cubeService;
    }

    public void setCubeService(String cubeService) {
        this.cubeService = cubeService;
    }

    public String getLockExecutorName() {
        return lockExecutorName;
    }

    public void setLockExecutorName(String lockExecutorName) {
        this.lockExecutorName = lockExecutorName;
    }

    public String getExecutorUnit() {
        return executorUnit;
    }

    public void setExecutorUnit(String executorUnit) {
        this.executorUnit = executorUnit;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
}

package us.zoom.cube.scheduler.lib.model;

import java.util.Date;

/**
 * @author: <PERSON><PERSON> Ding
 * @date: 2025/3/26 15:54
 * @desc:
 */
public class SchedulerExecutorServer {

    private String id;

    private String executorId;

    private String ip;

    private String host;

    //todo use enum?
    private String runStatus;

    private Date firstJoinTime;

    private Date updateTime;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getExecutorId() {
        return executorId;
    }

    public void setExecutorId(String executorId) {
        this.executorId = executorId;
    }

    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    public String getHost() {
        return host;
    }

    public void setHost(String host) {
        this.host = host;
    }

    public String getRunStatus() {
        return runStatus;
    }

    public void setRunStatus(String runStatus) {
        this.runStatus = runStatus;
    }

    public Date getFirstJoinTime() {
        return firstJoinTime;
    }

    public void setFirstJoinTime(Date firstJoinTime) {
        this.firstJoinTime = firstJoinTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
}

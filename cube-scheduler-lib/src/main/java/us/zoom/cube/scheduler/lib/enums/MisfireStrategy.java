package us.zoom.cube.scheduler.lib.enums;

/**
 * @author: <PERSON><PERSON>
 * @date: 2025/4/15 15:56
 * @desc:
 */
public enum MisfireStrategy {

    SKIP,
    EXECUTE,
    ;

    public static MisfireStrategy match(String value, MisfireStrategy defaultValue) {
        for (MisfireStrategy type : MisfireStrategy.values()) {
            if (type.name().equals(value)) {
                return type;
            }
        }
        return defaultValue;
    }

}

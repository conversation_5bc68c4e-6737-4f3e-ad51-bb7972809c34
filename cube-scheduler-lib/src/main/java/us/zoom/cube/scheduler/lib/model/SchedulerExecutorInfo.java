package us.zoom.cube.scheduler.lib.model;

import us.zoom.cube.scheduler.lib.enums.ExecutorType;

import java.util.Date;

/**
 * @author: <PERSON><PERSON> Ding
 * @date: 2025/3/26 15:54
 * @desc:
 */
public class SchedulerExecutorInfo {

    private String id;
    private String name;
    private String unit;
    private boolean enabled;
    private String tenantId;
    private ExecutorType executorType;
    private boolean domainModeEnabled;
    private String domainModeUrl;
    private Date createTime;
    private Date updateTime;
    private String creator;
    private String editor;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }

    public boolean isEnabled() {
        return enabled;
    }

    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
    }

    public String getTenantId() {
        return tenantId;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    public ExecutorType getExecutorType() {
        return executorType;
    }

    public void setExecutorType(ExecutorType executorType) {
        this.executorType = executorType;
    }

    public boolean isDomainModeEnabled() {
        return domainModeEnabled;
    }

    public void setDomainModeEnabled(boolean domainModeEnabled) {
        this.domainModeEnabled = domainModeEnabled;
    }

    public String getDomainModeUrl() {
        return domainModeUrl;
    }

    public void setDomainModeUrl(String domainModeUrl) {
        this.domainModeUrl = domainModeUrl;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getCreator() {
        return creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public String getEditor() {
        return editor;
    }

    public void setEditor(String editor) {
        this.editor = editor;
    }
}

package us.zoom.cube.scheduler.lib.utils;

import us.zoom.cube.scheduler.lib.cron.CronExpression;
import us.zoom.cube.scheduler.lib.enums.ScheduleType;

import java.util.Date;

/**
 * @author: <PERSON><PERSON> Ding
 * @date: 2025/4/30 13:58
 * @desc: copy from xxljob JobScheduleHelper generateNextValidTime
 */
public class ScheduleUtil {

    public static Date generateNextValidTime(ScheduleType scheduleType, String shcheduleConf, Date fromTime) throws Exception {

        if (ScheduleType.CRON == scheduleType) {
            Date nextValidTime = new CronExpression(shcheduleConf).getNextValidTimeAfter(fromTime);
            return nextValidTime;
        } else if (ScheduleType.FIXED_RATE == scheduleType /*|| ScheduleTypeEnum.FIX_DELAY == scheduleTypeEnum*/) {
            return new Date(fromTime.getTime() + Integer.valueOf(shcheduleConf) * 1000);
        }
        return null;
    }

}

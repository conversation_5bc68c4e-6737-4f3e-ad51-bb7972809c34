
# MySQL data source
dataSource.url=******************************************************************************************************************************************************************************************************************************************************************************
#dataSource.url=******************************************************************************************************************************************************************************************************************************************************************************
dataSourceStandby.url=******************************************************************************************************************************************************************************************************************************************************************************
dataSource.csms_rotate_username_key= cube.dataSource.username
dataSource.csms_rotate_password_key= cube.dataSource.password
dataSourceStandby.csms_rotate_username_key= cube.dataSourceStandby.username
dataSourceStandby.csms_rotate_password_key= cube.dataSourceStandby.password
dataSource.maxActive=5
dataSource.minIdle=1

spring.autoconfigure.exclude=org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration,org.springframework.boot.autoconfigure.orm.jpa.HibernateJpaAutoConfiguration,org.springframework.boot.autoconfigure.jdbc.DataSourceTransactionManagerAutoConfiguration,org.springframework.boot.autoconfigure.orm.jpa.JpaRepositoriesAutoConfiguration

spring.jpa.open-in-view=false
spring.data.jpa.repositories.enabled=false

# Redis configuration
spring.data.redis.ssl.enabled=false
spring.data.redis.host=127.0.0.1
spring.data.redis.port=6379
#spring.data.redis.username=default
spring.data.redis.timeout=10000
#spring.data.redis.password=

# Log configuration
log.filePath=cube-executor-pull-query/logs

# Async MQ configuration
async.mq.endpoint=https://asyncmq.zoomdev.us
async.mq.username=app_cube
#async.mq.password=
async.mq.producer.pool.size=4

# CSMS configuration
csms.enable=true
csms.app.path=ds01/cube
csms.endpoints=https://csmsdev.zoomdev.us
csms.app.credential.file=/usr/local/credentials
csms.jwt.public.key.path.list=dev/cube,ds01/cube


cube.config.client.endpoint=https://cubeconfig-perf-new.zoomdev.us

mysql.datasource.enabled=false

cube.config.dao.enable=false

cube.server.env=local


# JPA Configuration
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.MySQL8Dialect
spring.jpa.show-sql= true
spring.jpa.hibernate.ddl-auto= none
spring.jpa.hibernate.naming.implicit-strategy=org.springframework.boot.orm.jpa.hibernate.SpringImplicitNamingStrategy
spring.jpa.hibernate.naming.physical-strategy=org.hibernate.boot.model.naming.CamelCaseToUnderscoresNamingStrategy
spring.jpa.database= MYSQL
spring.jpa.properties.hibernate.enable_lazy_load_no_trans= true
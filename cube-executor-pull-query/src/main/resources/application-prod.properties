# MySQL data source
dataSource.url=******************************************************************************************************************************************************************************************************************************************************************************
dataSourceStandby.url=******************************************************************************************************************************************************************************************************************************************************************************
dataSource.csms_rotate_username_key= cube.dataSource.username
dataSource.csms_rotate_password_key= cube.dataSource.password
dataSourceStandby.csms_rotate_username_key= cube.dataSourceStandby.username
dataSourceStandby.csms_rotate_password_key= cube.dataSourceStandby.password
#cube.dataSource.username=
#cube.dataSource.password=
#cube.dataSourceStandby.username=
#cube.dataSourceStandby.password=

# Redis configuration
spring.data.redis.ssl.enabled=false
spring.data.redis.host=127.0.0.1
spring.data.redis.port=6379
#spring.data.redis.username=default
spring.data.redis.timeout=10000
#spring.data.redis.password=

# Log configuration
log.filePath=cube-executor-pull-query/logs

# Async MQ configuration
async.mq.endpoint=https://asyncmq.zoom.us
async.mq.username=app_cube
#async.mq.password=
async.mq.producer.pool.size=4
cube.server.env=standby
# CSMS configuration
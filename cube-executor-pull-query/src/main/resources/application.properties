# Server configuration
server.port=8081
spring.application.name=cube-executor-pull-query

# Profile configuration
spring.profiles.active=dev_local


# Clickhouse configuration
cube.clickhouse.flush.major.interval.millsecond=10000
cube.clickhouse.flush.major.interval.high.speed.millsecond=3000
cube.clickhouse.flush.minor.interval.millsecond=5000
cube.clickhouse.flush.max.batch.size=100000
cube.clickhouse.flush.min.batch.size=100
cube.clickhouse.thread.pool.size=1
cube.clickhouse.flush.parallelism=4
cube.clickhouse.queue.memory.limit=5368709120
cube.clickhouse.single.table.limit=209715200
cube.clickhouse.queue.size.limit=1500000

# Schedule task configuration
schedule.task.topic=cube_executor_schedule_task_topic
schedule.task.group=cube_executor_schedule_consumer
schedule.task.thread.count=1

# Async MQ configuration
async.mq.endpoint=
async.mq.username=
async.mq.password=
async.producer.pool.size=4

# Log configuration
log.filePath=cube-executor-pull-query/logs
log.sampling.count.info=1000
log.sampling.count.warn=1000
log.sampling.count.error=10

# CSMS configuration
csms.enable=true
csms.endpoints=https://csmsdev.zoomdev.us
csms.app.path=dev/cube
csms.app.credential.file=/opt/csms/credentials
csms.jwt.public.key.path.list=dev/cube,ds01/cube

# Task topic configuration
executor.task.topic=cube_pull_query_alarm
derived.metric.topic=cube_derived_metric_MAIN

# Redis configuration
spring.data.redis.enabled=true
package us.zoom.executor.service;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import us.zoom.cube.lib.utils.JsonUtils;
import us.zoom.infra.dao.model.SysParaDO;
import us.zoom.infra.enums.SysParaEnums;
import us.zoom.infra.syspara.SysParaEventHandlerIntf;
import us.zoom.infra.syspara.SysParaEventService;

import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * @author: canyon.li
 * @date: 2025/06/16
 **/
@Component
@Slf4j
public class PullQueryParaService implements SysParaEventHandlerIntf {


    private final AtomicReference<Map<String, String>> paramMap = new AtomicReference<>(new HashMap<>());

    private static final String PULL_QUERY_SWITCH = "pullQuerySwitch";

    private final AtomicReference<PullQuerySwitch> pullQuerySwitchRef = new AtomicReference<>(null);

    private static final int MAX_CONTINUOUS_FAIL_TIMES = 3;

    private static final int MAX_FAIL_TIMES_24H = 20;

    public PullQueryParaService() {
        SysParaEventService.registeSysparaListener(this, Collections.singletonList(SysParaEnums.cubeAlarm.name()));
    }

    public boolean isDerivedMetricProcessEnabled() {
        return Optional.ofNullable(pullQuerySwitchRef.get()).map(PullQuerySwitch::isDerivedMetricProcessEnabled).orElse(false);
    }

    public boolean isRedisDisabled() {
        return !Optional.ofNullable(pullQuerySwitchRef.get()).map(PullQuerySwitch::isRedisEnabled).orElse(false);
    }

    public int getMaxContinuousFailTimes() {
        return Optional.ofNullable(pullQuerySwitchRef.get()).map(PullQuerySwitch::getMaxContinuousFailTimes).orElse(MAX_CONTINUOUS_FAIL_TIMES);
    }

    public int getMaxFailTimes24h() {
        return Optional.ofNullable(pullQuerySwitchRef.get()).map(PullQuerySwitch::getMaxFailTimes24h).orElse(MAX_FAIL_TIMES_24H);
    }

    @Override
    public boolean onEventWhenHasData(List<SysParaDO> sysParas) {

        try{
            log.info("refresh pull query system param");
            Map<String, String> temp = Optional.of(sysParas).orElse(new ArrayList<>()).stream().collect(Collectors.toMap(SysParaDO::getParaKey, SysParaDO::getValue, (a, b) -> a));
            paramMap.set(temp);
        } catch (Exception e){
            log.error("init pull query system param exception, e",e);
        }

        try{
            String paramValue = paramMap.get().get(PULL_QUERY_SWITCH);
            if(StringUtils.isEmpty(paramValue)){
                return true;
            }
            PullQuerySwitch pullQuerySwitch = JsonUtils.toObject(paramValue, PullQuerySwitch.class);
            if(pullQuerySwitch != null){
                pullQuerySwitchRef.set(pullQuerySwitch);
            }

        }catch (Exception e){
            log.error("init incidentConfig param exception, e",e);
        }
        return true;
    }

    public int getParamIntValue(String key, int defaultValue) {
        try {
            String paramValue = paramMap.get().get(key);
            if (StringUtils.isEmpty(paramValue)) {
                return defaultValue;
            }
            return Integer.parseInt(paramValue.trim());
        } catch (Exception e) {
            log.error("PullQueryParaService convert system param value to integer failed for key:{}", key);
        }
        return defaultValue;
    }

    public boolean getParamBooleanValue(String key, boolean defaultValue) {
        try {
            String paramValue = paramMap.get().get(key);
            if (StringUtils.isEmpty(paramValue)) {
                return defaultValue;
            }
            return Boolean.parseBoolean(paramValue.trim());
        } catch (Exception e) {
            log.error("PullQueryParaService convert system param value to boolean failed for key:{}", key);
        }
        return defaultValue;
    }

    @Override
    public boolean onEventWhenNoData() {
        return false;
    }

    @Data
    public static class PullQuerySwitch {
        private boolean derivedMetricProcessEnabled = false;
        private boolean redisEnabled = false;
        private Integer maxContinuousFailTimes;
        private Integer maxFailTimes24h;
    }
}

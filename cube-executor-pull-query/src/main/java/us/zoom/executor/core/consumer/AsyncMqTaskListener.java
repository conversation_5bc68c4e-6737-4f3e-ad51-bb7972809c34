package us.zoom.executor.core.consumer;

import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.DependsOn;
import org.springframework.stereotype.Service;
import us.zoom.executor.core.infra.AsyncMQInstance;

/**
 * AsyncMQ task listener for handling scheduled tasks
 * 
 * <AUTHOR>
 */
@Service
@Slf4j
@DependsOn("asyncMQInstance")
public class AsyncMqTaskListener {
    
    @Value("${executor.task.topic}")
    private String taskTopic;
    
    @Autowired
    private ScheduleTaskConsumer scheduleTaskConsumer;
    
    /**
     * Initialize AsyncMQ message listener
     * Registers a consumer for the task topic
     */
    @PostConstruct
    public void initAsyncMqMsgListener() {
        try {
            // Ensure consumer groupId is unique
            String taskConsumerGroup = "cube_executor_task_group";
            AsyncMQInstance.getInstance().registerConsumer(taskTopic, taskConsumerGroup, 1, scheduleTaskConsumer);
            log.info("Successfully registered task consumer for topic: {}, group: {}", taskTopic, taskConsumerGroup);
        } catch (Exception e) {
            log.error("Failed to register task consumer for topic: {}", taskTopic, e);
        }
    }
} 
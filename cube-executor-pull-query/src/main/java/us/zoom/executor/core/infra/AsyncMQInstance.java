package us.zoom.executor.core.infra;

import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.RandomUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;
import us.zoom.cloud.secrets.spring.annotation.SecretValue;
import us.zoom.infra.asyncmq.AsyncMQRotateHandler;
import us.zoom.mq.client.AsyncMQ;
import us.zoom.mq.client.DefaultAsyncMQ;
import us.zoom.mq.client.clients.consumer.Consumer;
import us.zoom.mq.client.clients.consumer.RetryableStraw;
import us.zoom.mq.client.clients.producer.Producer;
import us.zoom.mq.client.pojo.Subscriber;
import us.zoom.mq.common.enums.ProtocolStrategy;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ThreadLocalRandom;
import java.util.concurrent.TimeUnit;

/**
 * AsyncMQ instance management class
 * Handles initialization and management of AsyncMQ instances for consumers and producers
 *
 * <AUTHOR>
 */
@Service("asyncMQInstance")
@Slf4j
public class AsyncMQInstance {

    private volatile static Consumer consumer;
    private volatile static AsyncMQ asyncMQ;
    private volatile static AsyncMQInstance instance;
    private static final List<Producer> PRODUCERS = new ArrayList<>();
    private static final List<AsyncMQ> ASYNCMQS = new ArrayList<>();

    private static final int PRODUCER_POOL_SIZE = 4;

    @Autowired
    private Environment environment;

    @SecretValue("async.mq.endpoint")
    private String asyncMqEndpoint;

    @SecretValue("async.mq.username")
    private String asyncMqUsername;

    @SecretValue("async.mq.password")
    private String asyncMqPassword;

    private AsyncMQInstance() {
    }

    @PostConstruct
    public void initAsyncMQ() {
        try {
            AsyncMQInstance.init(asyncMqEndpoint, asyncMqUsername, asyncMqPassword);
        } catch (Exception e) {
            log.error("Failed to initialize AsyncMQ", e);
            throw new RuntimeException("Failed to initialize AsyncMQ", e);
        }
    }

    /**
     * Initialize AsyncMQ instance with connection details
     *
     * @param endpoint MQ endpoint
     * @param username MQ username
     * @param password MQ password
     */
    public static void init(String endpoint, String username, String password) {
        try {
            if (instance == null) {
                synchronized (AsyncMQInstance.class) {
                    if (instance == null) {
                        log.info("Initializing AsyncMQInstance");
                        if (StringUtils.isBlank(endpoint) || StringUtils.isBlank(username) || StringUtils.isBlank(password)) {
                            throw new RuntimeException("Endpoint, username and password cannot be empty");
                        }

                        // Initialize main AsyncMQ instance
                        asyncMQ = createAsyncMq(endpoint, username, password, "executor-main-client");
                        ASYNCMQS.add(asyncMQ);
                        consumer = asyncMQ.consumer();
                        AsyncMQRotateHandler.addMQListener(asyncMQ, "async.mq.password", username);

                        // Initialize producer pool
                        for (int i = 0; i < PRODUCER_POOL_SIZE; i++) {
                            AsyncMQ producerMq = createAsyncMq(endpoint, username, password, "executor-async-client-" + i);
                            Producer producer = producerMq.producer();
                            producer.setProtocolStrategy(ProtocolStrategy.SIMPLE);
                            PRODUCERS.add(producer);
                            ASYNCMQS.add(producerMq);
                            AsyncMQRotateHandler.addMQListener(producerMq, "async.mq.password", username);
                        }

                        consumer.start();
                        instance = new AsyncMQInstance();
                        log.info("AsyncMQInstance initialization completed successfully");
                    }
                }
            }
        } catch (Exception e) {
            log.error("Failed to initialize AsyncMQInstance", e);
            throw new RuntimeException("Failed to initialize AsyncMQInstance", e);
        }
    }

    /**
     * Create AsyncMQ instance with configuration
     */
    private static AsyncMQ createAsyncMq(String endpoint, String username, String password, String clientId) {
        DefaultAsyncMQ asyncMq = new DefaultAsyncMQ(endpoint, username, password);
        asyncMq.setClientId(clientId);
        return asyncMq;
    }

    /**
     * Register a consumer for a specific topic
     *
     * @param topic Topic name
     * @param groupId Consumer group ID
     * @param receiveCount Number of messages to receive
     * @param batchHandler Message handler
     * @param <T> Message type
     */
    public <T> void registerConsumer(String topic, String groupId, int receiveCount, RetryableStraw<T> batchHandler) {
        try {
            if (consumer == null) {
                throw new IllegalStateException("Consumer not initialized");
            }
            Subscriber subscriber = consumer.registerSubscriber(topic, groupId);
            consumer.setStraw(subscriber, batchHandler);
            consumer.start(subscriber, receiveCount, 1);
            log.info("Consumer started - topic: {}, group id: {}, receiveCount: {}", topic, groupId, receiveCount);
        } catch (Exception e) {
            log.error("Failed to register consumer - topic: {}, groupId: {}", topic, groupId, e);
            throw new RuntimeException(String.format("Failed to register consumer - topic: %s, groupId: %s, threadCount: %d", 
                topic, groupId, receiveCount), e);
        }
    }

    /**
     * Get the main AsyncMQ instance
     *
     * @return AsyncMQ instance
     */
    public static AsyncMQ getAsyncMQ() {
        if (asyncMQ == null) {
            throw new IllegalStateException("AsyncMQ not initialized");
        }
        return asyncMQ;
    }

    /**
     * Get the singleton instance of AsyncMQInstance
     *
     * @return AsyncMQInstance instance
     */
    public static AsyncMQInstance getInstance() {
        if (instance == null) {
            throw new IllegalStateException("AsyncMQInstance not initialized");
        }
        return instance;
    }

    /**
     * Get a producer from the producer pool
     *
     * @return Producer instance
     */
    public Producer getProducer() {
        if (PRODUCERS.isEmpty()) {
            throw new IllegalStateException("No producers available");
        }
        return PRODUCERS.get(ThreadLocalRandom.current().nextInt(PRODUCER_POOL_SIZE));
    }

    /**
     * Shutdown all AsyncMQ instances
     */
    public void shutdown() {
        log.info("Shutting down AsyncMQ instances");
        ASYNCMQS.forEach(mq -> {
            try {
                mq.shutdown();
            } catch (Exception e) {
                log.error("Error shutting down AsyncMQ instance", e);
            }
        });
        ASYNCMQS.clear();
        PRODUCERS.clear();
        consumer = null;
        asyncMQ = null;
        instance = null;
    }
} 
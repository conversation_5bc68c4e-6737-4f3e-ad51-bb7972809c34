package us.zoom.executor.core.metric;

/**
 * <AUTHOR>
 */

public enum PullQueryMonitorTypeEnum {
    /**
     * Task start execution
     */
    TASK_START,
    
    /**
     * Task execution completed
     */
    TASK_END,
    
    /**
     * SQL generation
     */
    SQL_GENERATE,
    
    /**
     * SQL execution
     */
    SQL_EXECUTE,

    /**
     * No-Data Metric processing
     */
    NO_DATA_METRIC_PROCESS,
    
    /**
     * Metric processing
     */
    METRIC_PROCESS,
    
    /**
     * Metric sending
     */
    METRIC_SEND,
    
    /**
     * Task error
     */
    TASK_ERROR,

    /**
     * task status update
     */
    TASK_STATUS_UPDATE;
}
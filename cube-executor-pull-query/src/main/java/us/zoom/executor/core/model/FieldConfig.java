package us.zoom.executor.core.model;

import lombok.Data;
import java.util.List;

/**
 * Configuration for field types in query results
 * Defines which fields should be treated as numeric or string values when processing query results.
 * This configuration is used to properly extract and format field values from database query results.
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
public class FieldConfig {
    /**
     * List of field names to be treated as numeric values
     * These fields will be processed as numbers when extracting from query results
     */
    private List<String> number;
    
    /**
     * List of field names to be treated as string values
     * These fields will be processed as strings when extracting from query results
     */
    private List<String> string;
} 
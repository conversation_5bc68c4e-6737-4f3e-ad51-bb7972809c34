package us.zoom.executor.core.utils;

import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.collections4.MapUtils;

import java.util.Map;
import java.util.TreeMap;

/**
 * Tag utility class
 * Utility methods for handling metric tags
 *
 * <AUTHOR>
 */
public class TagUtils {

    /**
     * Create a unique key based on the tag Map
     * Sort tag values by key alphabetically, concatenate them, and calculate MD5 value
     *
     * @param tags Tag Map
     * @return MD5 value, returns empty string if tags is empty
     */
    public static String createKey(Map<String, String> tags) {
        if (MapUtils.isEmpty(tags)) {
            return "";
        }

        // Use TreeMap to automatically sort and stream connection strings directly
        return new TreeMap<>(tags).values().stream()
                .collect(StringBuilder::new, (sb, v) -> sb.append(v).append("_"), StringBuilder::append)
                .toString()
                .transform(DigestUtils::md5Hex);
    }
} 
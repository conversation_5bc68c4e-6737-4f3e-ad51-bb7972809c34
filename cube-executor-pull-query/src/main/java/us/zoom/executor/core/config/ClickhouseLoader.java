package us.zoom.executor.core.config;

import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import us.zoom.cube.config.client.api.ConfigApi;
import us.zoom.infra.dao.model.ClickhouseClusterDO;
import us.zoom.infra.dao.model.EnvironmentDO;

import java.util.List;
import java.util.stream.Collectors;

/**
 * @author: canyon.li
 * @date: 2025/05/15
 **/
@Component
@Slf4j
public class ClickhouseLoader implements CacheLoader  {

    @Autowired
    private ConfigApi configApi;

    @Autowired
    private ClickhouseHandlerFactory clickhouseHandlerFactory;

    @Autowired
    private RsaService rsaService;

    @Value("${cube.server.env:}")
    private String serverEnv;

    @Override
    public void load() {
        try {
            List<ClickhouseClusterDO> clusters = configApi.getAllClickhouseClusters();
            if (clickhouseHandlerFactory.isRunOnLocal()) {
                clusters = clusters.stream()
                        .filter(u -> "local".equals(u.getEnvironment()))
                        .peek(u -> u.setIsDefault(true))
                        .collect(Collectors.toList());
            } else {
                clusters = clusters.stream()
                        .filter(u -> !"local".equals(u.getEnvironment()))
                        .collect(Collectors.toList());
            }
            if (CollectionUtils.isEmpty(clusters)) {
                log.warn("No cluster has been defined");
                return;
            }
            List<ClickhouseClusterDO> copyList = Lists.newArrayList();
            for (ClickhouseClusterDO source : clusters) {
                ClickhouseClusterDO copy = new ClickhouseClusterDO();
                try {
                    BeanUtils.copyProperties(source, copy);
                    copy.setUsername(rsaService.decrypt(copy.getUsername()));
                    copy.setPassword(rsaService.decrypt(copy.getPassword()));
                } catch (Exception e) {
                    log.warn("Decrypt error for cluster {} {}", copy.getId(), copy.getName());
                }
                copyList.add(copy);
            }
            clickhouseHandlerFactory.get().refresh(copyList, configApi.getAllClickhouseClusterRelations(), configApi.getAllTenants());
            clickhouseHandlerFactory.get().setEnvironment(serverEnv);
            clickhouseHandlerFactory.getClickhouseWriter().setAllEnvs(configApi.getAllEnvironments().stream().map(EnvironmentDO::getName).collect(Collectors.toSet()));
            log.info("Done load clickhouse handlers,size ={}", copyList.size());
            //remove dead host
            clickhouseHandlerFactory.get().actualizeAll();
            log.info("Done refresh clickhouse handlers");
        } catch (Exception e) {
            log.error("Load Clickhouse error! ", e);
        }
    }
}

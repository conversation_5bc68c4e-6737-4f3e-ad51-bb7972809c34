package us.zoom.executor.core.config;

import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import us.zoom.cube.config.client.api.ConfigApi;
import us.zoom.infra.dao.model.SysParaDO;
import us.zoom.infra.syspara.SysParaEventService;

import java.util.List;

/**
 * @author: canyon.li
 * @date: 2025/06/10
 **/
@Component
@Slf4j
public class SystemParaCacheLoader implements CacheLoader {

    @Autowired
    private ConfigApi configApi;

    @Autowired
    private ConfigCache configCache;

    private final SysParaEventService sysParaEventService = new SysParaEventService();

    /**
     * type: alarmSwitch
     */
    private static final String CUBE_ALARM_SWITCH = "cubeAlarm";


    @Override
    public void load() {
        List<SysParaDO> sysParaDOList = configApi.getSysParaByTypes(Lists.newArrayList(CUBE_ALARM_SWITCH));
        sysParaEventService.loadInternal(sysParaDOList);
    }


}

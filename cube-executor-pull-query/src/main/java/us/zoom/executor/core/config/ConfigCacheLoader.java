package us.zoom.executor.core.config;

import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import us.zoom.infra.thread.CacheLoaderScheduler;

import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * Configuration cache loader for cube-executor-pull-query
 * <AUTHOR>
 */
@Component
@Slf4j
public class ConfigCacheLoader {

    private static final int LOAD_INTERVAL_IN_SECONDS = 60;
    private static final int LOAD_INITIAL_DELAY = 0;

    @Autowired
    private List<CacheLoader> cacheLoaders;

    @PostConstruct
    public void startLoad() {
        // Load immediately when application starts
        loadCfg2Cache();
        
        // Schedule periodic loading
        CacheLoaderScheduler.getInstance().getScheduler().scheduleAtFixedRate(
            this::loadCfg2Cache,
            LOAD_INITIAL_DELAY,
            LOAD_INTERVAL_IN_SECONDS,
            TimeUnit.SECONDS
        );
    }

    private void loadCfg2Cache() {
        long begin = System.currentTimeMillis();
        log.info("Begin loading configurations to cache!");

        for (CacheLoader loader : cacheLoaders) {
            try {
                loader.load();
            } catch (Exception e) {
                log.error("Failed to load cache for loader: {}", loader.getClass().getName(), e);
            }
        }

        log.info("Finished loading configurations, cost={}ms", System.currentTimeMillis() - begin);
    }
}
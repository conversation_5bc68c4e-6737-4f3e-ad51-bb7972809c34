package us.zoom.executor.core.model;

import lombok.Data;
import java.util.List;
import us.zoom.cube.lib.common.TaskStatusEnum;
/**
 * Derived metric configuration
 * Contains all necessary information for derived metric calculation
 *
 * <AUTHOR>
 */
@Data
public class DerivedMetricCfg {

    /**
     * Unique identifier for the derived metric
     */
    private String id;

    /**
     * Associated metric ID
     */
    private String metricId;

    /**
     * Name of the derived metric
     */
    private String name;

    /**
     * tags of derived metric
     */
    private List<String> tags;

    /**
     * Units that make up this derived metric
     */
    private List<DerivedMetricUnitCfg> units;

    /**
     * Task status
     * @see us.zoom.cube.lib.common.TaskStatusEnum
     */
    private TaskStatusEnum status;

    /**
     * Task interval
     */
    private Integer checkInterval;

    /**
     * Query interval
     */
    private Integer interval;

    /**
     * Task timeout in seconds
     */
    private Integer timeout;

    /**
     * Description of the derived metric
     */
    private String description;

    /**
     * Error message of the last fail task
     */
    private String errorMessage;

    /**
     * sourceType, default is "clickhouse"
     */
    private String sourceType;

    /**
     * tenant name
     */
    private String tenantName;

    /**
     * tenant id
     */
    private String tenantId;

}
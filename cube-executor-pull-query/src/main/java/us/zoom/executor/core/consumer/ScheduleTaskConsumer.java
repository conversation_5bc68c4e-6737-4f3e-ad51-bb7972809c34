package us.zoom.executor.core.consumer;

import com.fasterxml.jackson.core.type.TypeReference;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import us.zoom.cube.lib.utils.JsonUtils;
import us.zoom.cube.scheduler.lib.model.SchedulerJobInfo;
import us.zoom.cube.scheduler.lib.model.TriggerParam;
import us.zoom.infra.asyncmq.CubeConsumer;
import us.zoom.mq.client.clients.consumer.RetryableStraw;
import us.zoom.mq.common.client.task.PayloadType;
import us.zoom.mq.common.entity.TaskEntity;
import us.zoom.mq.common.util.JsonUtil;

import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Objects;

/**
 * Consumer for schedule tasks from AsyncMQ
 * <AUTHOR>
 */
@Slf4j
@Component
public class ScheduleTaskConsumer implements RetryableStraw<String> {

    @Autowired
    private ScheduleTaskHandler scheduleTaskHandler;

    private final static TypeReference<String> TYPE_REFERENCE = new TypeReference<>() {
    };

    @Override
    public boolean onMessage(List<TaskEntity<String>> taskEntities) {
        try {
            if (CollectionUtils.isEmpty(taskEntities)) {
                return false;
            }

            for (TaskEntity<String> taskEntity : taskEntities) {
                try {
                    String message = transferMessageToString(taskEntity.getPayloadType(), taskEntity.getPayload());
                    TriggerParam triggerParam = JsonUtils.toObject(message, TriggerParam.class);
                    
                    if (triggerParam == null) {
                        log.warn("Invalid task message: {}", message);
                        continue;
                    }

                    scheduleTaskHandler.handleTask(triggerParam);
                } catch (Exception e) {
                    log.error("Failed to process task: {}", taskEntity, e);
                }
            }
        } catch (Exception e) {
            log.error("Error processing schedule tasks", e);
            return true;
        }
        return false;
    }

    private String transferMessageToString(PayloadType payloadType, Object messageObj) {
        if (Objects.isNull(payloadType)) {
            if (messageObj == null) {
                throw new IllegalArgumentException("messageObj cannot be null when payloadType is null");
            }
            return (String) messageObj;
        }
        if (messageObj == null) {
            throw new IllegalArgumentException("messageObj cannot be null for payloadType " + payloadType);
        }
        return switch (payloadType) {
            case BYTES -> new String((byte[]) messageObj, StandardCharsets.UTF_8);
            case STRING -> (String) messageObj;
            case OBJECT -> JsonUtil.toJson(messageObj);
            default ->
                    throw new IllegalArgumentException("Only supports bytes, string, and object type async mq messages");
        };
    }

    @Override
    public TypeReference<String> type() {
        return TYPE_REFERENCE;
    }
}
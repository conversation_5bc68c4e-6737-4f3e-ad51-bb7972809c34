package us.zoom.executor.core.consumer;

import com.zoom.op.monitor.domain.DerivedMetric;
import lombok.Data;
import us.zoom.cube.lib.utils.JsonUtils;
import us.zoom.cube.scheduler.lib.model.TriggerParam;
import us.zoom.executor.core.enums.TaskType;

import java.io.Serial;
import java.io.Serializable;

/**
 * Represents a scheduled task to be executed
 * Contains all necessary information for task execution
 * 
 * <AUTHOR>
 */
@Data
public class ScheduleTask implements Serializable {
    
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * The derived metric configuration for the task
     */
    private DerivedMetric derivedMetric;

    /**
     * Current tenant
     */
    private String tenantName;

    /**
     * The trigger parameter for the task
     */
    private TriggerParam triggerParam;

    /**
     * Unique identifier for the task
     */
    private String taskId;
    
    /**
     * Type of the task (e.g., PULL_QUERY)
     */
    private TaskType taskType;
    
    /**
     * Time when the task should be executed (millisecond)
     */
    private long scheduleTime;

    public ScheduleTask(TriggerParam triggerParam) {
        DerivedMetric derivedMetric = JsonUtils.toObject(triggerParam.getJobParam(), DerivedMetric.class);
        this.derivedMetric = derivedMetric;
        this.triggerParam = triggerParam;
        this.taskId = derivedMetric.getId();
        this.taskType = TaskType.PULL_QUERY;
        this.scheduleTime = triggerParam.getTriggerTime();
    }
}

package us.zoom.executor.core.enums;

import lombok.Getter;

/**
 * Task type enumeration for executor
 * Defines the supported types of tasks that can be processed by the executor
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Getter
public enum TaskType {
    /**
     * Task type for pull query operations
     */
    PULL_QUERY("PULL_QUERY");

    /**
     * String representation of the task type
     * -- GETTER --
     *  Gets the string representation of the task type
     *

     */
    private final String type;

    /**
     * Constructor for TaskType enum
     *
     * @param type the string representation of the task type
     */
    TaskType(String type) {
        this.type = type;
    }

    /**
     * Converts a string to its corresponding TaskType enum value
     *
     * @param type the string representation of the task type
     * @return the corresponding TaskType enum value, or null if no match is found
     */
    public static TaskType fromString(String type) {
        for (TaskType taskType : TaskType.values()) {
            if (taskType.type.equalsIgnoreCase(type)) {
                return taskType;
            }
        }
        return null;
    }
}
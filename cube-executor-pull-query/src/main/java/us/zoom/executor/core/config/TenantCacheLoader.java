package us.zoom.executor.core.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import us.zoom.cube.config.client.api.ConfigApi;
import us.zoom.infra.dao.model.TenantDO;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import us.zoom.executor.core.model.TenantCfg;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
@Order(1)
public class TenantCacheLoader implements CacheLoader {

    @Autowired
    private ConfigCache configCache;

    @Autowired
    private ConfigApi configApi;

    @Override
    public void load() {
        long begin = System.currentTimeMillis();
        log.info("Begin loading tenants to configCache!");
        List<TenantDO> tenants = configApi.getAllTenants();
        log.info("tenantDOs size = {}",tenants.size());
        if (CollectionUtils.isEmpty(tenants)) {
            return;
        }
        Map<String, TenantCfg> tenantMap = createTenantMap(tenants);
        configCache.setTenantMap(tenantMap);
        log.info("load tenants over ,cost ={}", System.currentTimeMillis() - begin);

    }

    private Map<String, TenantCfg> createTenantMap(List<TenantDO> tenants) {
        List<TenantCfg> tenantCfg = tenants.stream().map(this::transToTenant).toList();
        return tenantCfg.stream().collect(Collectors.toMap(TenantCfg::getId, e -> e));
    }

    private TenantCfg transToTenant(TenantDO tenantDO) {
        TenantCfg tenant = new TenantCfg();
        tenant.setId(tenantDO.getId());
        tenant.setName(tenantDO.getName());
        return tenant;
    }
}
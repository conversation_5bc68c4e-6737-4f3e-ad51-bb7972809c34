package us.zoom.executor.core.model;

import lombok.Data;

/**
 * Configuration for a derived metric unit
 * Represents a single unit within a derived metric that defines how to calculate a specific aspect
 * of the metric using SQL templates and field configurations.
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
public class DerivedMetricUnitCfg {
    /**
     * Unique identifier for the derived metric unit
     */
    private String id;
    
    /**
     * Reference to the parent derived metric
     */
    private String derivedMetricId;
    
    /**
     * Name of the derived metric unit
     */
    private String name;
    
    /**
     * Type of the derived metric unit
     */
    private String type;
    
    /**
     * SQL template used for querying data
     * May contain placeholders like :from:, :to:, :interval: that will be replaced at runtime
     */
    private String sqlTemplate;
    
    /**
     * Configuration for fields to be extracted from query results
     * Defines which fields should be treated as numeric or string values
     */
    private FieldConfig fields;
} 
package us.zoom.executor.core.sender;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import us.zoom.cube.lib.utils.JsonUtils;
import us.zoom.executor.core.infra.AsyncMQInstance;
import us.zoom.mq.client.clients.producer.Producer;
import us.zoom.mq.common.Result;
import us.zoom.mq.common.client.task.Task;
import us.zoom.mq.common.response.ProduceResult;
import java.nio.charset.StandardCharsets;

/**
 * Metrics sender class
 * Handles sending metrics data to message queue
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class MetricsSender {

    @Value("${derived.metric.topic:us_cube_metrics}")
    private String derivedMetricTopic;

    @Autowired
    private AsyncMQInstance asyncMQInstance;

    /**
     * Send metrics data to message queue
     *
     * @param metricsData Metrics data to send
     * @param key Unique key for the metrics
     * @return true if send successfully, false otherwise
     */
    public boolean sendMetrics(Object metricsData, String key) {
        try {
            Task<byte[]> task = createMetricsTask(metricsData, key);
            Producer producer = asyncMQInstance.getProducer();
            Result<ProduceResult> result = producer.sendSync(task);
            
            if (result.isSuccess()) {
                log.debug("Successfully sent metrics data with key: {}", key);
                return true;
            } else {
                log.error("Failed to send metrics data with key: {}, reason: {}", key, result.getReason());
                return false;
            }
        } catch (Exception e) {
            log.error("Error sending metrics data with key: {}", key, e);
            return false;
        }
    }

    /**
     * Create a task for sending metrics data
     *
     * @param metricsData Metrics data to send
     * @param key Unique key for the metrics
     * @return Task object
     */
    private Task<byte[]> createMetricsTask(Object metricsData, String key) {
        Task<byte[]> task = new Task<>();
        task.setTopicName(derivedMetricTopic);
        task.setTaskType("metrics_data");
        task.setKey(key);
        task.setPayload(JsonUtils.toJsonString(metricsData).getBytes(StandardCharsets.UTF_8));
        return task;
    }
} 
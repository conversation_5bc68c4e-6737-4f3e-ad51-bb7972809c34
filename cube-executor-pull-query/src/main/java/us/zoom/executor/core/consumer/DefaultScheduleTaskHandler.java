package us.zoom.executor.core.consumer;

import cn.hutool.core.lang.Assert;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import us.zoom.cube.lib.common.TaskStatusEnum;
import us.zoom.cube.lib.integrations.Metrics;
import us.zoom.cube.lib.integrations.MetricsField;
import us.zoom.cube.lib.integrations.MetricsFieldTypeEnum;
import us.zoom.cube.lib.utils.JsonUtils;
import us.zoom.cube.scheduler.lib.model.TriggerParam;
import us.zoom.cube.sdk.util.MonitorLogReporter;
import us.zoom.executor.core.config.ClickhouseHandlerFactory;
import us.zoom.executor.core.config.ConfigCache;
import us.zoom.executor.core.enums.TaskType;
import us.zoom.executor.core.metric.PullQueryMonitor;
import us.zoom.executor.core.metric.PullQueryMonitorTypeEnum;
import us.zoom.executor.core.model.DerivedMetricCfg;
import us.zoom.executor.core.model.DerivedMetricUnitCfg;
import us.zoom.executor.core.model.FieldConfig;
import us.zoom.executor.core.sender.MetricsSender;
import us.zoom.executor.core.utils.TagUtils;
import us.zoom.executor.service.PullQueryParaService;
import us.zoom.infra.clickhouse.BucketReplace;
import us.zoom.infra.clickhouse.ClickhouseSqlUtil;
import us.zoom.infra.redis.RedisService;

import java.sql.Timestamp;
import java.util.*;

/**
 * Default implementation of ScheduleTaskHandler
 * Handles different types of scheduled tasks with distributed lock support
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class DefaultScheduleTaskHandler implements ScheduleTaskHandler {
    
    /** Prefix for task lock keys in Redis */
    private static final String TASK_LOCK_KEY_PREFIX = "task:lock:";
    
    /** Prefix for task status keys in Redis */
    private static final String TASK_STATUS_KEY_PREFIX = "task:status:";
    
    /** Prefix for task window keys in Redis */
    private static final String TASK_WINDOW_KEY_PREFIX = "task:window:";
    
    /** Lock timeout in minutes */
    private static final long LOCK_TIMEOUT_MINUTES = 1;
    
    /** Task status hash fields */
    private static final String FIELD_LAST_STATUS = "last_status";
    private static final String FIELD_LAST_EXEC_TIME = "last_exec_time";
    private static final String FIELD_CONSECUTIVE_FAILURES = "consecutive_failures";
    private static final String FIELD_TOTAL_FAILURES_24H = "total_failures_24h";
    
    /** Task status values */
    private static final String STATUS_SUCCESS = "SUCCESS";
    private static final String STATUS_FAILED = "FAILED";
    
    /** 24-hour time window in milliseconds */
    private static final long TIME_WINDOW_24H = 24 * 60 * 60 * 1000L;
    
    /** 1-hour time window in milliseconds */
    private static final long TIME_WINDOW_1H = 60 * 60 * 1000L;
    
    /** Maximum window size */
    private static final int MAX_WINDOW_SIZE = 100;

    private static final int MAX_CONTINUOUS_FAIL_TIMES = 3;

    /**
     * config self monitor
     */
    private final Logger monitorLog = LoggerFactory.getLogger("Monitor");

    @Autowired
    private RedisService redisService;
    
    @Autowired
    private ConfigCache configCache;

    @Autowired
    private ClickhouseHandlerFactory clickhouseHandlerFactory;

    @Autowired
    private MetricsSender metricsSender;

    @Autowired
    private PullQueryParaService pullQueryParaService;
    
    /**
     * Handles a scheduled task
     * This method manages the entire lifecycle of task execution including:
     * - Task type validation
     * - Lock acquisition
     * - Status checking
     * - Configuration retrieval
     * - Task execution
     * - Status updates
     *
     * @param triggerParam The scheduled task to handle
     */
    @Override
    public void handleTask(TriggerParam triggerParam) {

        // Check if derived metric process is enabled
        if (!pullQueryParaService.isDerivedMetricProcessEnabled()) {
            return;
        }

        ScheduleTask scheduleTask = new ScheduleTask(triggerParam);
        String taskId = scheduleTask.getTaskId();
        boolean lockAcquired = false;
        boolean taskSuccess = false;
        long startTime = System.currentTimeMillis();

        try {
            // Record task start monitoring
            printMonitor(PullQueryMonitorTypeEnum.TASK_START, scheduleTask.getTenantName(), scheduleTask.getDerivedMetric().getName(), taskId,
                startTime - triggerParam.getTriggerTime(), JsonUtils.toJsonStringIgnoreExp(scheduleTask), null, 1);

            // Acquire distributed lock
            lockAcquired = acquireTaskLock(taskId);
            if (!lockAcquired) {
                log.warn("Failed to acquire lock for task: {}", taskId);
                printMonitor(PullQueryMonitorTypeEnum.TASK_ERROR, scheduleTask.getTenantName(), scheduleTask.getDerivedMetric().getName(), taskId,
                    System.currentTimeMillis() - startTime, null, "Failed to acquire lock", 0);
                return;
            }

            // Check task status
            if (shouldSkipTask(taskId, startTime, scheduleTask)) {
                //TODO change status of derived metric
                return;
            }

            // Get derived metric configuration
            DerivedMetricCfg derivedMetricCfg = getDerivedMetricCfg(taskId);
            if (derivedMetricCfg == null) {
                log.warn("Derived metric not found for task: {}", taskId);
                printMonitor(PullQueryMonitorTypeEnum.TASK_ERROR, scheduleTask.getTenantName(), scheduleTask.getDerivedMetric().getName(), taskId,
                    System.currentTimeMillis() - startTime, null, "Derived metric not found", 0);
                return;
            }

            // Check if metric is enabled
            if (!isMetricEnabled(derivedMetricCfg)) {
                log.warn("Derived metric is disabled for task: {}", taskId);
                printMonitor(PullQueryMonitorTypeEnum.TASK_ERROR, scheduleTask.getTenantName(), scheduleTask.getDerivedMetric().getName(), taskId,
                        System.currentTimeMillis() - startTime, null, "Derived metric is disabled", 0);
                return;
            }

            scheduleTask.setTenantName(derivedMetricCfg.getTenantName());
            taskSuccess = executeTask(scheduleTask.getTaskType(), scheduleTask, derivedMetricCfg);
            
        } catch (Exception e) {
            log.error("Failed to handle derived metric task: {}", taskId, e);
            taskSuccess = false;
            printMonitor(PullQueryMonitorTypeEnum.TASK_ERROR, scheduleTask.getTenantName(), scheduleTask.getDerivedMetric().getName(), taskId,
                System.currentTimeMillis() - startTime, null, e.getMessage(), 0);
        } finally {
            if (lockAcquired) {
                try {
                    releaseTaskLock(taskId);
                } catch (Exception e) {
                    log.error("Failed to release lock for task: {}", taskId, e);
                }
            }
            // Update task status
            updateTaskStatus(scheduleTask, taskId, taskSuccess);
            // Record task end monitoring
            printMonitor(PullQueryMonitorTypeEnum.TASK_END, scheduleTask.getTenantName(), scheduleTask.getDerivedMetric().getName(), taskId,
                System.currentTimeMillis() - startTime, String.valueOf(taskSuccess), null, taskSuccess ? 1 : 0);
        }
    }

    /**
     * Acquires a distributed lock for the task
     *
     * @param taskId The task identifier
     * @return true if lock was acquired, false otherwise
     */
    private boolean acquireTaskLock(String taskId) {
        if (pullQueryParaService.isRedisDisabled()) {
            return true;
        }
        if (StringUtils.isBlank(taskId)) {
            return false;
        }
        String lockKey = TASK_LOCK_KEY_PREFIX + taskId;
        return redisService.setRedisLock(lockKey, "1", LOCK_TIMEOUT_MINUTES);
    }

    /**
     * Releases the distributed lock for the task
     *
     * @param taskId The task identifier
     */
    private void releaseTaskLock(String taskId) {
        if (pullQueryParaService.isRedisDisabled()) {
            return;
        }
        if (StringUtils.isBlank(taskId)) {
            return;
        }
        String lockKey = TASK_LOCK_KEY_PREFIX + taskId;
        redisService.delete(lockKey);
    }

    /**
     * get the task status in Redis
     * Uses Redis Hash structure to store task status information
     *
     * @param taskId The task identifier
     * @return Task status information Map
     */
    private Map<String, Object> getTaskStatus(String taskId) {
        if (pullQueryParaService.isRedisDisabled()) {
            return createEmptyTaskStatus();
        }
        if (StringUtils.isBlank(taskId)) {
            return Collections.emptyMap();
        }
        
        String statusKey = TASK_STATUS_KEY_PREFIX + taskId;
        Map<String, Object> statusInfo = redisService.hgetAll(statusKey);
        
        if (statusInfo == null || statusInfo.isEmpty()) {
            statusInfo = createEmptyTaskStatus();
            redisService.hmset(statusKey, statusInfo);
        }
        
        return statusInfo;
    }

    private Map<String, Object> createEmptyTaskStatus() {
        Map<String, Object> statusInfo = new HashMap<>(4);
        statusInfo.put(FIELD_LAST_STATUS, STATUS_SUCCESS);
        statusInfo.put(FIELD_LAST_EXEC_TIME, String.valueOf(System.currentTimeMillis()));
        statusInfo.put(FIELD_CONSECUTIVE_FAILURES, "0");
        statusInfo.put(FIELD_TOTAL_FAILURES_24H, "0");
        return statusInfo;
    }

    /**
     * check the task status in Redis
     * Uses Redis Hash structure to store task status information
     *
     * @param taskId The task identifier
     * @return if task status is success
     */
    private boolean checkTaskStatus(String taskId) {
       return true;
    }

    /**
     * Updates the task status in Redis
     * Updates various status fields including:
     * - Last execution status
     * - Last execution time
     * - Consecutive failures count
     * - 24-hour failure count
     * - Sliding window status records
     *
     * @param scheduleTask The schedule task
     * @param taskId The task identifier
     * @param success Whether the task execution was successful
     */
    private void updateTaskStatus(ScheduleTask scheduleTask, String taskId, boolean success) {
        long startTime = System.currentTimeMillis();
        if (pullQueryParaService.isRedisDisabled()) {
            return;
        }
        if (StringUtils.isBlank(taskId)) {
            return;
        }
        
        try {
            Map<String, Object> statusInfo = getTaskStatus(taskId);
            if (statusInfo == null) {
                statusInfo = new HashMap<>();
            }

            long currentTime = System.currentTimeMillis();
            long lastExecTime = statusInfo.containsKey(FIELD_LAST_EXEC_TIME) ? 
                Long.parseLong(statusInfo.get(FIELD_LAST_EXEC_TIME).toString()) : 0;

            // Check if within 24h time window before updating lastExecTime
            if (currentTime - lastExecTime <= TIME_WINDOW_24H) {
                int totalFailures = statusInfo.containsKey(FIELD_TOTAL_FAILURES_24H) ? 
                    Integer.parseInt(statusInfo.get(FIELD_TOTAL_FAILURES_24H).toString()) : 0;
                
                if (!success) {
                    totalFailures++;
                    statusInfo.put(FIELD_TOTAL_FAILURES_24H, String.valueOf(totalFailures));
                }
            } else {
                // Reset 24h failure count if outside time window
                statusInfo.put(FIELD_TOTAL_FAILURES_24H, success ? "0" : "1");
            }

            // Update last execution time and status
            statusInfo.put(FIELD_LAST_EXEC_TIME, String.valueOf(currentTime));
            statusInfo.put(FIELD_LAST_STATUS, success ? STATUS_SUCCESS : STATUS_FAILED);

            // Update consecutive failures
            int consecutiveFailures = statusInfo.containsKey(FIELD_CONSECUTIVE_FAILURES) ? 
                Integer.parseInt(statusInfo.get(FIELD_CONSECUTIVE_FAILURES).toString()) : 0;
            
            if (!success) {
                consecutiveFailures++;
            } else {
                consecutiveFailures = 0;
            }
            statusInfo.put(FIELD_CONSECUTIVE_FAILURES, String.valueOf(consecutiveFailures));

            // Save status
            String statusKey = TASK_STATUS_KEY_PREFIX + taskId;
            redisService.hmset(statusKey, statusInfo);
            
            // Update sliding window status
            updateSlidingWindow(taskId, currentTime, success ? STATUS_SUCCESS : STATUS_FAILED);

            printMonitor(PullQueryMonitorTypeEnum.TASK_STATUS_UPDATE, scheduleTask.getTenantName(), scheduleTask.getDerivedMetric().getName(), taskId,
                    System.currentTimeMillis() - startTime, null, null, 1);
        } catch (Exception e) {
            log.error("Failed to update task status: {}", taskId, e);
            printMonitor(PullQueryMonitorTypeEnum.TASK_STATUS_UPDATE, scheduleTask.getTenantName(), scheduleTask.getDerivedMetric().getName(), taskId,
                    System.currentTimeMillis() - startTime, null, e.getMessage(), 0);
        }
    }

    /**
     * Updates the sliding window status for a task
     * Uses Redis Sorted Set to store task execution status with timestamps
     *
     * @param taskId The task identifier
     * @param timestamp The execution timestamp
     * @param status The execution status
     */
    private void updateSlidingWindow(String taskId, long timestamp, String status) {
        if (pullQueryParaService.isRedisDisabled()) {
            return;
        }
        String windowKey = TASK_WINDOW_KEY_PREFIX + taskId;
        
        // Add new status with timestamp as score
        redisService.zadd(windowKey, status, timestamp);
        
        // Remove old entries (older than 1 hour)
        long cutoffTime = System.currentTimeMillis() - TIME_WINDOW_1H;
        redisService.zremrangeByScore(windowKey, 0, cutoffTime);
        
        // Trim to maximum size if needed
        long size = redisService.zcard(windowKey);
        if (size > MAX_WINDOW_SIZE) {
            redisService.zremrangeByRank(windowKey, 0, size - MAX_WINDOW_SIZE - 1);
        }
    }

    /**
     * Gets the recent execution statuses for a task
     *
     * @param taskId The task identifier
     * @return Map of timestamp to status for recent executions
     */
    private Map<Long, String> getRecentStatuses(String taskId) {
        if (pullQueryParaService.isRedisDisabled()) {
            return Collections.emptyMap();
        }
        String windowKey = TASK_WINDOW_KEY_PREFIX + taskId;
        Set<Object> statuses = redisService.zrange(windowKey, 0, -1);
        Map<Long, String> result = new LinkedHashMap<>();
        
        for (Object status : statuses) {
            Double score = redisService.zscore(windowKey, status);
            if (score != null) {
                result.put(score.longValue(), status.toString());
            }
        }
        
        return result;
    }

    /**
     * Checks if a task has consecutive failed
     *
     * @param taskId The task identifier
     * @param threshold The number of consecutive timeouts to check for
     * @return true if the task has consecutive failed exceeding the threshold
     */
    private boolean hasConsecutiveFailed(String taskId, int threshold) {
        Map<Long, String> recentStatuses = getRecentStatuses(taskId);
        int consecutiveTimeouts = 0;
        
        for (String status : recentStatuses.values()) {
            if (STATUS_FAILED.equals(status)) {
                consecutiveTimeouts++;
                if (consecutiveTimeouts >= threshold) {
                    return true;
                }
            } else {
                consecutiveTimeouts = 0;
            }
        }
        
        return false;
    }

    /**
     * Retrieves derived metric configuration from cache
     *
     * @param taskId The task identifier
     * @return DerivedMetricCfg object if found, null otherwise
     */
    private DerivedMetricCfg getDerivedMetricCfg(String taskId) {
        if (StringUtils.isBlank(taskId)) {
            return null;
        }
        
        // Get from cache
        DerivedMetricCfg derivedMetricCfg = configCache.getDerivedMetricCfg(taskId);
        if (derivedMetricCfg != null) {
            log.debug("Found derived metric in cache: {}", taskId);
            return derivedMetricCfg;
        }
        
        // If not found in cache, log warning
        log.warn("Derived metric not found in cache: {}", taskId);
        return null;
    }
    
    /**
     * Checks if the metric is enabled
     *
     * @param derivedMetricCfg The derived metric configuration
     * @return true if the metric is enabled, false otherwise
     */
    private boolean isMetricEnabled(DerivedMetricCfg derivedMetricCfg) {
        return derivedMetricCfg != null && derivedMetricCfg.getStatus() != null && TaskStatusEnum.ENABLED.equals(derivedMetricCfg.getStatus());
    }
    
    /**
     * Executes the task based on its type and configuration
     *
     * @param taskType The type of the task
     * @param scheduleTask The task information
     * @param derivedMetricCfg The derived metric configuration
     * @return true if task execution was successful, false otherwise
     */
    private boolean executeTask(TaskType taskType, ScheduleTask scheduleTask, DerivedMetricCfg derivedMetricCfg) {
        if (Objects.requireNonNull(taskType) == TaskType.PULL_QUERY) {
            return handlePullQueryTask(scheduleTask, derivedMetricCfg);
        } else {
            log.error("Unhandled task type: {}", taskType);
            return false;
        }
    }

    /**
     * Handles pull query type tasks
     *
     * @param scheduleTask The task information
     * @param derivedMetricCfg The derived metric configuration
     * @return true if task execution was successful, false otherwise
     */
    private boolean handlePullQueryTask(ScheduleTask scheduleTask, DerivedMetricCfg derivedMetricCfg) {
        log.info("Handling pull query task: {}, derived metric: {}", JsonUtils.toJsonStringIgnoreExp(scheduleTask), derivedMetricCfg.getName());
        long startTime = System.currentTimeMillis();

        try {
            // Get unit configuration
            DerivedMetricUnitCfg unitCfg = getUnitConfig(derivedMetricCfg);
            if (unitCfg == null) {
                printMonitor(PullQueryMonitorTypeEnum.TASK_ERROR, scheduleTask.getTenantName(), derivedMetricCfg.getName(), scheduleTask.getTaskId(),
                    System.currentTimeMillis() - startTime, null, "No unit config found", 0);
                return false;
            }

            // Generate SQL
            long sqlStartTime = System.currentTimeMillis();
            String sql = generateSql(scheduleTask.getScheduleTime(), derivedMetricCfg, unitCfg);
            printMonitor(PullQueryMonitorTypeEnum.SQL_GENERATE, scheduleTask.getTenantName(), derivedMetricCfg.getName(), scheduleTask.getTaskId(),
                System.currentTimeMillis() - sqlStartTime, sql, null, 1);

            // Execute SQL
            long executeStartTime = System.currentTimeMillis();
            List<Map<String, Object>> results = executeSql(scheduleTask, sql);
            if (results == null) {
                printMonitor(PullQueryMonitorTypeEnum.SQL_EXECUTE, scheduleTask.getTenantName(), derivedMetricCfg.getName(), scheduleTask.getTaskId(),
                    System.currentTimeMillis() - executeStartTime, null, "SQL execution failed", 0);
                return false;
            }
            printMonitor(PullQueryMonitorTypeEnum.SQL_EXECUTE, scheduleTask.getTenantName(), derivedMetricCfg.getName(), scheduleTask.getTaskId(),
                System.currentTimeMillis() - executeStartTime, String.valueOf(results.size()), null, 1);

            // Process results and send metrics
            long processStartTime = System.currentTimeMillis();
            processAndSendMetrics(scheduleTask, derivedMetricCfg, unitCfg, results);
            printMonitor(PullQueryMonitorTypeEnum.METRIC_PROCESS, scheduleTask.getTenantName(), derivedMetricCfg.getName(), scheduleTask.getTaskId(),
                System.currentTimeMillis() - processStartTime, String.valueOf(results.size()), null, 1);

            return true;
        } catch (Exception e) {
            log.error("Failed to execute pull query task: {}", scheduleTask.getTaskId(), e);
            printMonitor(PullQueryMonitorTypeEnum.TASK_ERROR, scheduleTask.getTenantName(), derivedMetricCfg.getName(), scheduleTask.getTaskId(),
                System.currentTimeMillis() - startTime, null, e.getMessage(), 0);
        }
        return false;
    }

    /**
     * Get the first unit configuration from derived metric configuration
     *
     * @param derivedMetricCfg The derived metric configuration
     * @return The first unit configuration, or null if not found
     */
    private DerivedMetricUnitCfg getUnitConfig(DerivedMetricCfg derivedMetricCfg) {
        List<DerivedMetricUnitCfg> units = derivedMetricCfg.getUnits();
        if (units == null || units.isEmpty()) {
            log.warn("No unit config found for derived metric: {}", derivedMetricCfg.getName());
            return null;
        }

        String sqlTemplate = units.get(0).getSqlTemplate();
        if (sqlTemplate == null) {
            log.warn("No sqlTemplate found in unit config for derived metric: {}", derivedMetricCfg.getName());
            return null;
        }

        return units.get(0);
    }

    /**
     * Generate SQL query based on task and configuration
     *
     * @param triggerTime trigger time
     * @param derivedMetricCfg The derived metric configuration
     * @param unitCfg The unit configuration
     * @return The generated SQL query
     */
    private String generateSql(Long triggerTime, DerivedMetricCfg derivedMetricCfg, DerivedMetricUnitCfg unitCfg) {
        long interval = derivedMetricCfg.getInterval();
        String intervalStr = interval + " SECOND ";
        long toTime = triggerTime;
        String toStr = "toDateTime(" + (toTime / 1000) + ")";
        long fromTime = toTime - interval * 1000L;
        String fromStr = "toDateTime(" + (fromTime / 1000) + ")";

        BucketReplace bucketReplace = new BucketReplace();
        String template = bucketReplace.replace(unitCfg.getSqlTemplate());

        String sql = template
                .replaceAll("(?i):interval:", intervalStr)
                .replaceAll("(?i):_from_:", fromStr)
                .replaceAll("(?i):_to_:", toStr);
        String encodeSql = ClickhouseSqlUtil.tableNameEncoding(sql);
        log.info("Generated SQL: {}", encodeSql);
        return encodeSql;
    }


    public static void main(String[] args) {
        String sql = """
                WITH current_hour AS (
                    SELECT\s
                        sum("value") AS sum_value,\s
                        toStartOfInterval(time, INTERVAL 1 MINUTE) AS time,\s
                        "dimensionValue"
                    FROM "Infra_Monitor_Cube_Cost_Center"."cloudwatch_metrics"
                    WHERE time > :dashboardTime: AND time < :upperDashboardTime:
                    AND "metrics" = 'BytesOutToDestination'
                    GROUP BY time, "dimensionValue"
                ),
                previous_hour AS (
                    SELECT\s
                        sum("value") AS sum_value,\s
                        toStartOfInterval(time + INTERVAL 1 MINUTE, INTERVAL 1 MINUTE) AS time,\s
                        "dimensionValue"
                    FROM "Infra_Monitor_Cube_Cost_Center"."cloudwatch_metrics"
                    WHERE time > :dashboardTime: - INTERVAL 1 MINUTE AND time < :upperDashboardTime: - INTERVAL 1 MINUTE
                    AND "metrics" = 'BytesOutToDestination'
                    GROUP BY time, "dimensionValue"
                )
                SELECT\s
                    c.time,
                    c.dimensionValue,
                    c.sum_value AS current_hour_value,
                    p.sum_value AS previous_hour_value,
                    (c.sum_value - p.sum_value) / p.sum_value * 100 AS growth_rate
                FROM current_hour c
                LEFT JOIN previous_hour p
                ON c.time = p.time AND c.dimensionValue = p.dimensionValue
                group by c.time, dimensionValue
                ORDER BY c.time;
                """;

        BucketReplace bucketReplace = new BucketReplace();
        String template = bucketReplace.replace(sql);
        String intervalStr = "1 SECOND ";
        long toTime = System.currentTimeMillis() - 300 * 1000L;
        String toStr = "toDateTime(" + (toTime / 1000) + ")";
        long fromTime = toTime - 3 * 1000L;
        String fromStr = "toDateTime(" + (fromTime / 1000) + ")";
        String resultSql = template
                .replaceAll("(?i):interval:", intervalStr)
                .replace(":from:", fromStr)
                .replace(":to:", toStr);
        String encodeSql = ClickhouseSqlUtil.tableNameEncoding(sql);
        System.out.println("Generated SQL: " + encodeSql);
    }

    /**
     * Execute SQL query and return results
     *
     * @param scheduleTask The task information
     * @param sql The SQL query to execute
     * @return List of query results, or null if execution failed
     */
    private List<Map<String, Object>> executeSql(ScheduleTask scheduleTask, String sql) {
        long startTime = System.currentTimeMillis();
        try {
            log.info("Starting SQL execution for task: {}, tenant: {}, sql: {}", scheduleTask.getTaskId(), scheduleTask.getTenantName(), sql);
            List<Map<String, Object>> results = clickhouseHandlerFactory.get().query(scheduleTask.getTenantName(),sql);
            long endTime = System.currentTimeMillis();
            long duration = endTime - startTime;

            if (results == null) {
                log.error("Failed to execute SQL query for task: {}, duration: {}ms", scheduleTask.getTaskId(), duration);
                return null;
            }

            log.info("SQL execution completed for task: {}, duration: {}ms, result size: {}",
                    scheduleTask.getTaskId(), duration, results.size());
            
            // Log slow query if duration exceeds threshold (e.g., 5 seconds)
            if (duration > 5000) {
                log.warn("Slow SQL query detected for task: {}, duration: {}ms, sql: {}",
                        scheduleTask.getTaskId(), duration, sql);
            }

            return results;
        } catch (Exception e) {
            long endTime = System.currentTimeMillis();
            long duration = endTime - startTime;
            log.error("Failed to execute SQL query for task: {}, duration: {}ms, error: {}",
                    scheduleTask.getTaskId(), duration, e.getMessage(), e);
            throw e;
        }
    }

    /**
     * Process query results and send metrics
     *
     * @param scheduleTask The schedule task
     * @param derivedMetricCfg The derived metric configuration
     * @param unitCfg The unit configuration
     * @param results The query results to process
     */
    private void processAndSendMetrics(ScheduleTask scheduleTask, DerivedMetricCfg derivedMetricCfg, DerivedMetricUnitCfg unitCfg, List<Map<String, Object>> results) {
        long startTime = System.currentTimeMillis();
        Map<String, List<Metrics>> metricsMap = new HashMap<>();
        
        if (results.isEmpty()) {
            // Handle no data case
            Metrics nodataMetric = createNodataMetric(derivedMetricCfg, scheduleTask.getScheduleTime());
            String key = generateKey(derivedMetricCfg, nodataMetric.getTags());
            metricsMap.computeIfAbsent(key, k -> new ArrayList<>()).add(nodataMetric);
            log.info("No data found for task: {}, creating nodata metric", scheduleTask.getTaskId());
            printMonitor(PullQueryMonitorTypeEnum.NO_DATA_METRIC_PROCESS, scheduleTask.getTenantName(), derivedMetricCfg.getName(), scheduleTask.getTaskId(),
                System.currentTimeMillis() - startTime, "No data found, created nodata metric", null, 1);
        } else {
            // Process normal results
            for (Map<String, Object> row : results) {
                try {
                    // Extract tags
                    Map<String, String> tags = extractTags(row, derivedMetricCfg);
                    String tagKey = generateKey(derivedMetricCfg, tags);
                    
                    // Extract fields
                    List<MetricsField> fields = extractFields(row, unitCfg);

                    long ts = Optional.ofNullable(row.get("time")).map(Timestamp.class::cast).map(Timestamp::getTime).orElse(scheduleTask.getScheduleTime());
                    // Create metrics
                    Metrics metrics = createMetrics(derivedMetricCfg, tags, fields, ts);
                    
                    // Add to metrics map
                    metricsMap.computeIfAbsent(tagKey, k -> new ArrayList<>()).add(metrics);
                } catch (Exception e) {
                    log.error("Failed to process row: {}", row, e);
                    printMonitor(PullQueryMonitorTypeEnum.METRIC_PROCESS, scheduleTask.getTenantName(), derivedMetricCfg.getName(), scheduleTask.getTaskId(),
                        System.currentTimeMillis() - startTime, JsonUtils.toJsonStringIgnoreExp(row), e.getMessage(), 0);
                }
            }
        }
        
        // Send metrics
        long sendStartTime = System.currentTimeMillis();
        try {
            sendMetrics(scheduleTask, metricsMap);
            printMonitor(PullQueryMonitorTypeEnum.METRIC_SEND, scheduleTask.getTenantName(), derivedMetricCfg.getName(), scheduleTask.getTaskId(),
                System.currentTimeMillis() - sendStartTime, getMetricsMapStatistics(metricsMap), null, 1);
        } catch (Exception e) {
            log.error("Failed to send metrics: {}", getMetricsMapStatistics(metricsMap), e);
            printMonitor(PullQueryMonitorTypeEnum.METRIC_SEND, scheduleTask.getTenantName(), derivedMetricCfg.getName(), scheduleTask.getTaskId(),
                System.currentTimeMillis() - sendStartTime, getMetricsMapStatistics(metricsMap), e.getMessage(), 0);
            throw e;
        }
    }

    /**
     * Extract tags from row data
     *
     * @param row The row data
     * @param derivedMetricCfg The derived metric configuration
     * @return Map of extracted tags
     */
    private Map<String, String> extractTags(Map<String, Object> row, DerivedMetricCfg derivedMetricCfg) {
        Map<String, String> tags = new HashMap<>();
        List<String> tagList = derivedMetricCfg.getTags();
        if (tagList != null) {
            for (String tag : tagList) {
                Object value = row.get(tag);
                if (value != null) {
                    tags.put(tag, value.toString());
                }
            }
        }
        return tags;
    }

    /**
     * Extract fields from row data
     *
     * @param row The row data
     * @param unitCfg The unit configuration
     * @return List of extracted fields
     */
    private List<MetricsField> extractFields(Map<String, Object> row, DerivedMetricUnitCfg unitCfg) {
        List<MetricsField> fields = new ArrayList<>();
        FieldConfig fieldConfig = unitCfg.getFields();
        if (fieldConfig != null) {
            extractNumberFields(row, fieldConfig, fields);
            extractStringFields(row, fieldConfig, fields);
        }
        return fields;
    }

    /**
     * Extract number fields from row data
     *
     * @param row The row data
     * @param fieldConfig The field configuration
     * @param fields The list to add extracted fields to
     */
    private void extractNumberFields(Map<String, Object> row, FieldConfig fieldConfig, List<MetricsField> fields) {
        if (fieldConfig.getNumber() != null) {
            Map<String, Double> numberMap = new HashMap<>(fieldConfig.getNumber().size());
            for (String fieldName : fieldConfig.getNumber()) {
                Object value = row.get(fieldName);
                if (value != null) {
                    numberMap.put(fieldName, Double.parseDouble(value.toString()));
                }
            }
            if (!numberMap.isEmpty()) {
                MetricsField metricsField = new MetricsField();
                metricsField.setFieldName("derivedFieldNumberMap");
                metricsField.setFieldType(MetricsFieldTypeEnum.mapNumber);
                metricsField.setFieldValue(numberMap);
                fields.add(metricsField);
            }
        }
    }

    /**
     * Extract string fields from row data
     *
     * @param row The row data
     * @param fieldConfig The field configuration
     * @param fields The list to add extracted fields to
     */
    private void extractStringFields(Map<String, Object> row, FieldConfig fieldConfig, List<MetricsField> fields) {
        if (fieldConfig.getString() != null) {
            Map<String, String> stringMap = new HashMap<>(fieldConfig.getString().size());
            for (String fieldName : fieldConfig.getString()) {
                Object value = row.get(fieldName);
                if (value != null) {
                    stringMap.put(fieldName, value.toString());
                }
            }
            if (!stringMap.isEmpty()) {
                MetricsField metricsField = new MetricsField();
                metricsField.setFieldName("derivedFieldStringMap");
                metricsField.setFieldType(MetricsFieldTypeEnum.mapString);
                metricsField.setFieldValue(stringMap);
                fields.add(metricsField);
            }
        }
    }

    /**
     * Create Metrics object
     *
     * @param derivedMetricCfg The derived metric configuration
     * @param tags The tags to set
     * @param fields The fields to set
     * @return The created Metrics object
     */
    private Metrics createMetrics(DerivedMetricCfg derivedMetricCfg, Map<String, String> tags, List<MetricsField> fields, long ts) {
        Metrics metric = new Metrics();
        metric.setMetricsId(derivedMetricCfg.getMetricId());
        metric.setMetricsName(derivedMetricCfg.getName());
        metric.setTs(ts);
        metric.setTags(tags);
        metric.setFields(fields);
        metric.setDerived(true);
        return metric;
    }

    /**
     * Generate unique key for metrics
     *
     * @param derivedMetricCfg The derived metric configuration
     * @param tags The tags to include in the key
     * @return The generated key
     */
    private String generateKey(DerivedMetricCfg derivedMetricCfg, Map<String, String> tags) {
        return derivedMetricCfg.getId() + "_" + TagUtils.createKey(tags);
    }

    public static final String CUBE_NODATA = "_CUBE_NO_DATA_";

    /**
     * Create a nodata metric
     *
     * @param derivedMetricCfg The derived metric configuration
     * @param triggerTime The task time
     * @return The created nodata metric
     */
    private Metrics createNodataMetric(DerivedMetricCfg derivedMetricCfg, Long triggerTime) {
        Metrics metric = new Metrics();
        metric.setMetricsId(derivedMetricCfg.getMetricId());
        metric.setMetricsName(derivedMetricCfg.getName());
        
        // Set tags
        Map<String, String> tags = new HashMap<>();
        List<String> tagList = derivedMetricCfg.getTags();
        if (tagList != null) {
            for (String tag : tagList) {
                tags.put(tag, CUBE_NODATA);
            }
        }
        metric.setTags(tags);
        metric.setDerived(true);
        // Mark this metric as 'nodata'
        metric.setNoData(true);
        
        // Set nodata field
        List<MetricsField> fields = new ArrayList<>();
//        MetricsField nodataField = new MetricsField();
//        nodataField.setFieldName();
//        nodataField.setFieldValue(1);
//        nodataField.setFieldType(MetricsFieldTypeEnum.number);
//        fields.add(nodataField);
        
        // Add timestamp field
        MetricsField timestampField = new MetricsField();
        timestampField.setFieldName("timestamp");
        timestampField.setFieldValue(triggerTime);
        timestampField.setFieldType(MetricsFieldTypeEnum.number);
        fields.add(timestampField);

        metric.setTs(triggerTime);
        metric.setFields(fields);
        return metric;
    }

    /**
     * Send collected metrics
     *
     * @param task The task information
     * @param metricsMap The map of metrics lists to send
     */
    private void sendMetrics(ScheduleTask task, Map<String, List<Metrics>> metricsMap) {
        if (!metricsMap.isEmpty()) {
            int totalMetrics = metricsMap.values().stream().mapToInt(List::size).sum();
            log.info("Sending {} metrics across {} keys for task: {}", totalMetrics, metricsMap.size(), task.getTaskId());
            
            for (Map.Entry<String, List<Metrics>> entry : metricsMap.entrySet()) {
                String key = entry.getKey();
                List<Metrics> metricsList = entry.getValue();
                
                log.debug("Sending {} metrics for key: {}", metricsList.size(), key);
                for (Metrics metric : metricsList) {
                    boolean sent = metricsSender.sendMetrics(metric, key);
                    Assert.isTrue(sent, "Failed to send metrics for task: {}, key: {}", task.getTaskId(), key);
                }
            }
            log.info("Finished sending metrics for task: {}", task.getTaskId());
        }
    }

    /**
     * Get statistics about the metrics map
     *
     * @param metricsMap The map of metrics lists
     * @return Statistics information
     */
    private String getMetricsMapStatistics(Map<String, List<Metrics>> metricsMap) {
        if (metricsMap.isEmpty()) {
            return "Empty metrics map";
        }
        
        int totalKeys = metricsMap.size();
        int totalMetrics = metricsMap.values().stream().mapToInt(List::size).sum();
        int maxMetricsPerKey = metricsMap.values().stream().mapToInt(List::size).max().orElse(0);
        int minMetricsPerKey = metricsMap.values().stream().mapToInt(List::size).min().orElse(0);
        
        // Count keys with multiple metrics
        long keysWithMultipleMetrics = metricsMap.values().stream().filter(list -> list.size() > 1).count();
        
        return String.format("Keys: %d, Total metrics: %d, Max per key: %d, Min per key: %d, Keys with multiple metrics: %d", 
                totalKeys, totalMetrics, maxMetricsPerKey, minMetricsPerKey, keysWithMultipleMetrics);
    }


    private void printMonitor(PullQueryMonitorTypeEnum monitorType, String serviceName, String metricName, String metricId, long cost, String data, String error, int status) {

        PullQueryMonitor pullQueryMonitor = new PullQueryMonitor();
        pullQueryMonitor.setMonitorType(monitorType.name());
        pullQueryMonitor.setServiceName(serviceName);
        pullQueryMonitor.setMetricName(metricName);
        pullQueryMonitor.setMetricId(metricId);
        pullQueryMonitor.setCost(cost);
        pullQueryMonitor.setData(data);
        pullQueryMonitor.setError(error);
        pullQueryMonitor.setStatus(status);
        pullQueryMonitor.setCurrentTs(System.currentTimeMillis());

        MonitorLogReporter.report(monitorLog, pullQueryMonitor);
    }

    /**
     * Check if task should be skipped based on its status
     * @param taskId task id
     * @param startTime task start time
     * @param scheduleTask schedule task
     * @return true if task should be skipped, false otherwise
     */
    private boolean shouldSkipTask(String taskId, long startTime, ScheduleTask scheduleTask) {
        // Check consecutive failures using hasConsecutiveFailed method
        int maxContinuousFailTimes = pullQueryParaService.getMaxContinuousFailTimes();
        if (hasConsecutiveFailed(taskId, maxContinuousFailTimes)) {
            log.warn("Task has consecutive failed {} times, skipping execution: {}", maxContinuousFailTimes, taskId);
            printMonitor(PullQueryMonitorTypeEnum.TASK_ERROR, scheduleTask.getTenantName(), scheduleTask.getDerivedMetric().getName(), taskId,
                System.currentTimeMillis() - startTime, null, "Task has consecutive failed " + maxContinuousFailTimes + " times", 0);
            return true;
        }

        Map<String, Object> taskStatus = getTaskStatus(taskId);
        if (taskStatus == null) {
            return false;
        }

        // Check 24h total failures
        Integer totalFailures24h = Optional.ofNullable(taskStatus.get(FIELD_TOTAL_FAILURES_24H)).map(Object::toString).map(Integer::parseInt).orElse(null);
        if (totalFailures24h != null && totalFailures24h >= pullQueryParaService.getMaxFailTimes24h()) {
            log.warn("Task has failed {} times in last 24h, skipping execution: {}", totalFailures24h, taskId);
            printMonitor(PullQueryMonitorTypeEnum.TASK_ERROR, scheduleTask.getTenantName(), scheduleTask.getDerivedMetric().getName(), taskId,
                System.currentTimeMillis() - startTime, null, "Task has failed " + totalFailures24h + " times in last 24h", 0);
            return true;
        }

        return false;
    }
}
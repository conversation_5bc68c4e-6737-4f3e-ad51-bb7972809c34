package us.zoom.executor.core.controller;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import us.zoom.cschedule.infra.utils.JsonUtils;
import us.zoom.cube.scheduler.lib.model.TriggerParam;
import us.zoom.executor.core.consumer.ScheduleTask;
import us.zoom.executor.core.consumer.ScheduleTaskHandler;

/**
 * Controller for schedule task operations
 * Provides REST API endpoints to trigger schedule tasks
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/schedule-tasks")
@Slf4j
public class ScheduleTaskController {

    @Autowired
    private ScheduleTaskHandler scheduleTaskHandler;

    /**
     * Endpoint to manually trigger a schedule task
     * 
     * @param triggerParam The schedule task to handle
     * @return Response indicating success or failure
     */
    @PostMapping("/execute")
    public ResponseEntity<String> executeTask(@RequestBody TriggerParam triggerParam) {
        try {
            scheduleTaskHandler.handleTask(triggerParam);
            return ResponseEntity.ok("Task execution initiated successfully");
        } catch (Exception e) {
            log.error("Failed to execute task: {}", JsonUtils.toJsonStringIgnoreExp(triggerParam), e);
            return ResponseEntity.internalServerError().body("Failed to execute task: " + e.getMessage());
        }
    }
    
    /**
     * Endpoint to check the status of the schedule task service
     * 
     * @return Response indicating the service is up and running
     */
    @GetMapping("/status")
    public ResponseEntity<String> getStatus() {
        return ResponseEntity.ok("Schedule task service is running");
    }
}
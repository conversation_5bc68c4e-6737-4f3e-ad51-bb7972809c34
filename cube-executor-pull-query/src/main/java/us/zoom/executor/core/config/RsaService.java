package us.zoom.executor.core.config;

import org.jetbrains.annotations.NotNull;
import org.springframework.context.EnvironmentAware;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;
import us.zoom.cloud.secrets.spring.annotation.SecretValue;
import us.zoom.infra.utils.RSAUtils;

/**
 * @author: canyon.li
 * @date: 2025/05/15
 **/
@Service
public class RsaService implements EnvironmentAware {

    private Environment environment;

    @SecretValue("common_private_key")
    private String commonPrivateKey;

    @SecretValue("common_public_key")
    private String commonPublicKey;


    @Override
    public void setEnvironment(@NotNull Environment environment) {
        this.environment = environment;
    }

    public  String encrypt( String input) throws Exception{
        return  RSAUtils.encode(input,commonPublicKey);
    }

    public  String decrypt(String input ) throws Exception{
        return  RSAUtils.decode(input,commonPrivateKey);
    }

}
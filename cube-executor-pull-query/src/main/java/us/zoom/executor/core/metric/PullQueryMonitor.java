package us.zoom.executor.core.metric;

import lombok.Data;
import us.zoom.cube.sdk.annotation.CubeMonitorLog;
import us.zoom.cube.sdk.annotation.Field;
import us.zoom.cube.sdk.annotation.Tag;

/**
 * @author: canyon.li
 * @date: 2025/06/16
 **/
@CubeMonitorLog(measure = "pullQueryMonitor")
@Data
public class PullQueryMonitor {

    /**
     * @see PullQueryMonitorTypeEnum
     */
    @Tag
    private String monitorType;

    @Tag
    private String serviceName;

    @Tag
    private String metricName;

    @Tag
    private String metricId;

    @Tag
    private int status;

    @Field
    private long cost;

    @Field
    private String data;

    @Field
    private String error;

    @Field
    private long currentTs;

}

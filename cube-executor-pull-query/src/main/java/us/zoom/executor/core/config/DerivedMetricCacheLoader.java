package us.zoom.executor.core.config;

import cn.hutool.core.collection.CollectionUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import com.zoom.op.monitor.domain.DerivedMetric;
import com.zoom.op.monitor.domain.DerivedMetricUnit;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.DependsOn;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import us.zoom.cube.config.client.api.ConfigApi;
import us.zoom.cube.lib.common.TaskStatusEnum;
import us.zoom.executor.core.model.DerivedMetricCfg;
import us.zoom.executor.core.model.DerivedMetricUnitCfg;
import us.zoom.executor.core.model.FieldConfig;
import us.zoom.cube.lib.utils.JsonUtils;
import org.apache.commons.lang3.StringUtils;
import us.zoom.executor.core.model.TenantCfg;
import us.zoom.infra.utils.CommonSplitConstants;

import java.util.*;
import java.util.stream.Collectors;
/**
 * Loader for derived metric configurations
 * Loads derived metrics from the config service and stores them in the config cache
 * @author: canyon.li
 * @date: 2025/05/15
 */
@Component
@Slf4j
@DependsOn("tenantCacheLoader")
public class DerivedMetricCacheLoader implements CacheLoader {

    @Autowired
    private ConfigApi configApi;

    @Autowired
    private ConfigCache configCache;
    

    /**
     * Loads derived metrics from the config service and updates the config cache
     */
    @Override
    public void load() {
        long begin = System.currentTimeMillis();
        log.info("Begin loading derived metrics to configCache!");

        try {
            // Fetch all derived metrics from config service
//            List<DerivedMetric> derivedMetrics = new ArrayList<>();
//            derivedMetrics = generateTestDerivedMetrics();
            List<DerivedMetric> derivedMetrics = configApi.getAllDerivedMetrics();


            log.info("derivedMetrics size = {}", derivedMetrics.size());

            if (CollectionUtils.isEmpty(derivedMetrics)) {
                configCache.setDerivedMetricCfgCache(Collections.emptyMap());
                return;
            }

            // Transform to configuration objects and store in cache
            Map<String, DerivedMetricCfg> derivedMetricCfgMap = createDerivedMetricCfgs(derivedMetrics);
            configCache.setDerivedMetricCfgCache(derivedMetricCfgMap);

            log.info("Loading derived metrics to configCache finished, time cost = {}ms", (System.currentTimeMillis() - begin));
        } catch (Exception e) {
            log.error("Error loading derived metrics", e);
        }
    }

    /**
     * Transforms DerivedMetric objects to DerivedMetricCfg objects
     *
     * @param derivedMetrics List of derived metrics from the database
     * @return Map of derived metric configurations keyed by metric ID
     */
    private Map<String, DerivedMetricCfg> createDerivedMetricCfgs(List<DerivedMetric> derivedMetrics) {
        return derivedMetrics.stream()
                .map(this::transformToDerivedMetricCfg)
                .filter(cfg -> CollectionUtil.isNotEmpty(cfg.getUnits()) && cfg.getStatus() != null && TaskStatusEnum.ENABLED.equals(cfg.getStatus()))
                .collect(Collectors.toMap(DerivedMetricCfg::getId, cfg -> cfg));
    }

    /**
     * Transforms a single DerivedMetric to a DerivedMetricCfg
     *
     * @param derivedMetric The derived metric data object
     * @return The derived metric configuration
     */
    private DerivedMetricCfg transformToDerivedMetricCfg(DerivedMetric derivedMetric) {
        DerivedMetricCfg cfg = new DerivedMetricCfg();

        // Copy basic properties
        cfg.setTenantId(derivedMetric.getTenantId());
        Optional.ofNullable(configCache.getTenantMap())
                .map(map -> map.get(derivedMetric.getTenantId()))
                .map(TenantCfg::getName)
                .ifPresent(cfg::setTenantName);
        cfg.setId(derivedMetric.getId());
        cfg.setMetricId(derivedMetric.getMetricId());
        cfg.setName(derivedMetric.getName());
        cfg.setTimeout(derivedMetric.getTimeout());
        cfg.setDescription(null);
        cfg.setSourceType(derivedMetric.getSourceType());
        cfg.setErrorMessage(derivedMetric.getErrorMessage());
        cfg.setInterval(derivedMetric.getInterval());
        cfg.setCheckInterval(derivedMetric.getCheckInterval());
        // Convert tags from comma-separated string to List<String>
        String tagsStr = derivedMetric.getTags();
        if (StringUtils.isNotBlank(tagsStr)) {
            List<String> tags = Arrays.stream(tagsStr.split(","))
                    .map(String::trim)
                    .filter(StringUtils::isNotBlank)
                    .collect(Collectors.toList());
            cfg.setTags(tags);
        } else {
            cfg.setTags(Collections.emptyList());
        }

        cfg.setStatus(derivedMetric.getStatus());
        // Transform units if present
        if (!CollectionUtils.isEmpty(derivedMetric.getUnits())) {
            List<DerivedMetricUnitCfg> unitCfgs = derivedMetric.getUnits().stream()
                    .map(this::transformToDerivedMetricUnitCfg)
                    .collect(Collectors.toList());
            cfg.setUnits(unitCfgs);
        } else {
            cfg.setUnits(Collections.emptyList());
        }

        return cfg;
    }

    /**
     * Transforms a DerivedMetricUnit to a DerivedMetricUnitCfg
     *
     * @param unitDO The derived metric unit data object
     * @return The derived metric unit configuration
     */
    private DerivedMetricUnitCfg transformToDerivedMetricUnitCfg(DerivedMetricUnit unitDO) {
        DerivedMetricUnitCfg unitCfg = new DerivedMetricUnitCfg();

        unitCfg.setId(unitDO.getId());
        unitCfg.setDerivedMetricId(unitDO.getDerivedMetricId());
        unitCfg.setName(unitDO.getName());
        unitCfg.setType(unitDO.getType());
        unitCfg.setSqlTemplate(unitDO.getSqlTemplate());

        // Convert fields from JSON string to FieldConfig object
        String fieldsStr = unitDO.getFields();
        if (StringUtils.isNotBlank(fieldsStr)) {
            try {
                FieldConfig fieldConfig = JsonUtils.toObjectByTypeRef(fieldsStr, new TypeReference<>() {
                });
                unitCfg.setFields(fieldConfig);
            } catch (Exception e) {
                log.error("Failed to parse fields JSON for unit: {}", unitDO.getId(), e);
                unitCfg.setFields(new FieldConfig());
            }
        } else {
            unitCfg.setFields(new FieldConfig());
        }

        return unitCfg;
    }

    /**
     *
     * @return
     */
    public String generateId()
    {
        String result= UUID.randomUUID().toString();

        if(result.contains(CommonSplitConstants.SPLIT)){
            result= result.replaceAll("_","-");
        }
        return result;
    }

    /**
     * Generates test derived metrics for development and testing purposes
     * 
     * @return List of test DerivedMetric objects
     */
    private List<DerivedMetric> generateTestDerivedMetrics() {
        log.info("Generating test derived metrics for development");
        List<DerivedMetric> testMetrics = new ArrayList<>();
        
        // Create a sample derived metric
        DerivedMetric metric1 = new DerivedMetric();
        String derivedMetricId = "canyon_derived_metric_1";
        metric1.setId(derivedMetricId);
        metric1.setMetricId(generateId());
        metric1.setName("canyon_derived_metric_1");
        metric1.setTags("instanceId");
        metric1.setStatus(TaskStatusEnum.ENABLED);
        metric1.setTimeout(60);
        metric1.setSourceType("clickhouse");
        metric1.setInterval(60);
        metric1.setCheckInterval(60);
        
        // Create a sample unit for the metric
        DerivedMetricUnit unit1 = new DerivedMetricUnit();
        unit1.setId(generateId());
        unit1.setDerivedMetricId(derivedMetricId);
        unit1.setName("canyon_derived_metric_unit_1");
        unit1.setType("SQL");
        unit1.setSqlTemplate("SELECT avg(us) AS avg_us, instanceId, toStartOfInterval(time, INTERVAL :interval:) AS time FROM Infra_Monitor_Cube_Alarm_cube_ch_env1_122d.cpu " +
                "WHERE time >= :from: AND time < :to: " +
                "GROUP BY time, instanceId");
        
        // Create field config for the unit
        FieldConfig fieldConfig = new FieldConfig();
        List<String> numberFields = new ArrayList<>();
        numberFields.add("avg_us");
        fieldConfig.setNumber(numberFields);
        
        List<String> stringFields = new ArrayList<>();
        fieldConfig.setString(stringFields);
        
        // Set fields JSON
        unit1.setFields(JsonUtils.toJsonString(fieldConfig));
        
        // Add unit to metric
        List<DerivedMetricUnit> units = new ArrayList<>();
        units.add(unit1);
        metric1.setUnits(units);
        
        // Add metric to test metrics list
        testMetrics.add(metric1);
        log.info("Generated {} test derived metrics", testMetrics.size());
        return testMetrics;
    }
}

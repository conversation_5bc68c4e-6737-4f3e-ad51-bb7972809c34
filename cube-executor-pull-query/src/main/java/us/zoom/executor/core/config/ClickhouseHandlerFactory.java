package us.zoom.executor.core.config;

import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.EnvironmentAware;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;
import us.zoom.infra.clickhouse.ClickhouseEnvProxy;
import us.zoom.infra.clickhouse.ClickhouseWriter;
import us.zoom.infra.clickhouse.HighSpeedWriter;

/**
 * @author: canyon.li
 * @date: 2025/05/15
 **/
@Getter
@Component
@Slf4j
@Configuration
public class ClickhouseHandlerFactory implements EnvironmentAware {

    private Environment environment;

    private ClickhouseWriter clickhouseWriter;

    @Setter
    private ClickhouseWriter highSpeedWriter;

    private ClickhouseEnvProxy clickhouseEnvProxy;

    private long flushMajorIntervalMillSecond;

    private long flushMinorIntervalMillSecond;

    private int flushMaxRecordCount;

    private int flushMinRecordCountPerTable;

    private boolean runOnLocal=false;


    @Override
    public void setEnvironment(Environment environment) {
        this.environment = environment;
        runOnLocal = environment.getProperty("spring.profiles.active", "").endsWith("_local");
        flushMajorIntervalMillSecond = Integer.parseInt(environment.getProperty("cube.clickhouse.flush.major.interval.millsecond", "10000"));
        long flushMajorIntervalForHighSpeedMillSecond = Integer.parseInt(environment.getProperty("cube.clickhouse.flush.major.interval.high.speed.millsecond", "3000"));
        flushMinorIntervalMillSecond = Integer.parseInt(environment.getProperty("cube.clickhouse.flush.minor.interval.millsecond", "5000"));
        flushMaxRecordCount = Integer.parseInt(environment.getProperty("cube.clickhouse.flush.max.batch.size", "100000"));
        flushMinRecordCountPerTable = Integer.parseInt(environment.getProperty("cube.clickhouse.flush.min.batch.size", "100"));
        int threadPoolSize = Integer.parseInt(environment.getProperty("cube.clickhouse.thread.pool.size", "1"));
        int flushParallelism = Integer.parseInt(environment.getProperty("cube.clickhouse.flush.parallelism", "4"));
        //queueMemoryLimit default 5GB
        long queueMemoryLimit= Long.parseLong(environment.getProperty("cube.clickhouse.queue.memory.limit","5368709120"));
        //singleTableMemoryLimit default 200MB
        long singleTableMemoryLimit= Long.parseLong(environment.getProperty("cube.clickhouse.single.table.limit","209715200"));
        int queueMax = Integer.parseInt(environment.getProperty("cube.clickhouse.queue.size.limit", "1500000"));

        clickhouseEnvProxy=new ClickhouseEnvProxy();
        clickhouseEnvProxy.setQueryEnv(environment.getProperty("cube.clickhouse.query.env", "standby"));
        clickhouseWriter=new ClickhouseWriter(this.clickhouseEnvProxy)
                .setFlushMajorIntervalMillSecond(flushMajorIntervalMillSecond).setFlushMinorIntervalMillSecond(flushMinorIntervalMillSecond)
                .setFlushMaxRecordCount(flushMaxRecordCount).setFlushMinRecordCountPerTable(flushMinRecordCountPerTable)
                .setThreadPoolSize(threadPoolSize).setFlushParallelism(flushParallelism)
                .setQueueMemoryLimit(queueMemoryLimit).setSingleTableMemoryLimit(singleTableMemoryLimit)
                .start();

        highSpeedWriter=new HighSpeedWriter(this.clickhouseEnvProxy)
                .setFlushMajorIntervalMillSecond(flushMajorIntervalForHighSpeedMillSecond)
                .setFlushMinorIntervalMillSecond(flushMajorIntervalForHighSpeedMillSecond)
                .setFlushMaxRecordCount(flushMaxRecordCount/10)
                .setThreadPoolSize(threadPoolSize)
                .setQueueMemoryLimit(queueMemoryLimit/10).setFlushParallelism(flushParallelism)
                .setQueueSizeMax(queueMax)
                .start();
    }

    public ClickhouseEnvProxy get(){
        return clickhouseEnvProxy;
    }

    public ClickhouseWriter getClickhouseWriter(boolean isHighSpeed){
        return isHighSpeed ? highSpeedWriter : clickhouseWriter;
    }

}

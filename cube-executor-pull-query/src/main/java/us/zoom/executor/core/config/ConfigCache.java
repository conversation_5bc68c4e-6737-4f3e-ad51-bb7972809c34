package us.zoom.executor.core.config;

import lombok.Data;
import lombok.Setter;
import org.springframework.stereotype.Component;
import us.zoom.executor.core.model.DerivedMetricCfg;
import us.zoom.executor.core.model.TenantCfg;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Configuration cache
 * <AUTHOR>
 */
@Component
@Data
public class ConfigCache {

    private String env;

    private String ip;

    /**
     * -- SETTER --
     *  Sets the derived metric configuration cache
     *
     */
    @Setter
    private Map<String, DerivedMetricCfg> derivedMetricCfgCache = new ConcurrentHashMap<>();

    @Setter
    private Map<String, TenantCfg> tenantMap;

    /**
     * Gets the derived metric configuration by ID
     * 
     * @param metricId The metric ID
     * @return The derived metric configuration, or null if not found
     */
    public DerivedMetricCfg getDerivedMetricCfg(String metricId) {
        return derivedMetricCfgCache.get(metricId);
    }
    
    /**
     * Gets all derived metric configurations
     * 
     * @return Map of all derived metric configurations
     */
    public Map<String, DerivedMetricCfg> getAllDerivedMetricCfgs() {
        return derivedMetricCfgCache;
    }
}
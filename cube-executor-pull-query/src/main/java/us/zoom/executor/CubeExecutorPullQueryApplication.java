package us.zoom.executor;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.autoconfigure.jdbc.DataSourceTransactionManagerAutoConfiguration;
import org.springframework.boot.autoconfigure.orm.jpa.HibernateJpaAutoConfiguration;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Import;
import us.zoom.cloud.secrets.spring.context.CSMSBeanDefinitionRegistrar;
//import us.zoom.executor.config.DatabaseConfiguration;

/**
 * Cube Executor Pull Query
 * <AUTHOR>
 */
@SpringBootApplication
@ComponentScan(basePackages = {
        "us.zoom.executor",
        "us.zoom.infra.asyncmq",
        "us.zoom.infra.redis",
//        "us.zoom.infra.dao",
        "us.zoom.cube.config.client",
        "com.zoom.op.monitor.config"})
@Import({CSMSBeanDefinitionRegistrar.class})
public class CubeExecutorPullQueryApplication {
    public static void main(String[] args) {
        // Add JVM parameters to allow access to the java.util.concurrent package
        System.setProperty("--add-opens", "java.base/java.util.concurrent=ALL-UNNAMED");
        SpringApplication.run(CubeExecutorPullQueryApplication.class, args);
    }
} 
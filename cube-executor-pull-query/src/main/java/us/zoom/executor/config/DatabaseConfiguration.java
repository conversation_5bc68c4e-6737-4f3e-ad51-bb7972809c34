//package us.zoom.executor.config;
//
//import com.zaxxer.hikari.HikariDataSource;
//import org.mybatis.spring.annotation.MapperScan;
//import org.springframework.context.EnvironmentAware;
//import org.springframework.context.annotation.Bean;
//import org.springframework.context.annotation.Configuration;
//import org.springframework.context.annotation.Primary;
//import org.springframework.core.env.Environment;
//import org.springframework.jdbc.datasource.DataSourceTransactionManager;
//
//import javax.sql.DataSource;
//
///**
// * 数据库配置类
// */
//@Configuration
//@Primary
//@MapperScan(basePackages = "us.zoom.executor.dao")
//public class DatabaseConfiguration implements EnvironmentAware {
//
//    private Environment environment;
//
//    @Override
//    public void setEnvironment(Environment environment) {
//        this.environment = environment;
//    }
//
//    @Primary
//    @Bean
//    public DataSource dataSource() {
//        HikariDataSource ds = new HikariDataSource();
//        ds.setJdbcUrl(environment.getProperty("dataSource.url"));
//        ds.setUsername(environment.getProperty("cube.dataSource.username"));
//        ds.setPassword(environment.getProperty("cube.dataSource.password"));
//        ds.setMaximumPoolSize(environment.getProperty("dataSource.maxActive", Integer.class));
//        ds.setMinimumIdle(environment.getProperty("dataSource.minIdle", Integer.class));
//        ds.setDriverClassName("com.mysql.cj.jdbc.Driver");
//        ds.setPoolName("Cube-Executor-Pull-Query");
//
//        return ds;
//    }
//
//    @Primary
//    @Bean
//    public DataSourceTransactionManager dataSourceTransactionManager() throws Exception {
//        return new DataSourceTransactionManager(dataSource());
//    }
//}
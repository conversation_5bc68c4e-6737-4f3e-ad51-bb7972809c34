package us.zoom.executor.core.consumer;

import com.zoom.op.monitor.domain.DerivedMetric;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import us.zoom.cube.lib.utils.JsonUtils;
import us.zoom.cube.scheduler.lib.model.TriggerParam;
import us.zoom.executor.CubeExecutorPullQueryApplication;
import us.zoom.executor.core.enums.TaskType;
import us.zoom.infra.redis.RedisService;

/**
 * Test class for DefaultScheduleTaskHandler
 * <AUTHOR>
 */
@ActiveProfiles("dev_local")
public class DefaultScheduleTaskHandlerTest extends CubeExecutorPullQueryApplicationTest {

    @Autowired
    private ScheduleTaskHandler scheduleTaskHandler;

    @Autowired
    private RedisService redisService;


    @BeforeAll
    public static void setUp() {
        // 添加 JVM 参数以允许访问 java.util.concurrent 包
        System.setProperty("--add-opens", "java.base/java.util.concurrent=ALL-UNNAMED");
    }

    @Test
    public void testHandleTask() {
        TriggerParam triggerParam = new TriggerParam();
        triggerParam.setJobId("canyon_derived_metric_1");
        triggerParam.setName("canyon_derived_metric_1");
        triggerParam.setTriggerTime(System.currentTimeMillis() - 300000);
        DerivedMetric derivedMetric = new DerivedMetric();
        derivedMetric.setId("canyon_derived_metric_1");
        triggerParam.setJobParam(JsonUtils.toJsonString(derivedMetric));

        scheduleTaskHandler.handleTask(triggerParam);
    }

    @Test
    public void test() {
        try {
            redisService.set("canyon_derived_metric_1", "canyon_derived_metric_2");
        } catch (Exception e) {
            e.printStackTrace();
        }

    }
} 
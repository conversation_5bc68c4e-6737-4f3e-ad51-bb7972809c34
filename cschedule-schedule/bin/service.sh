#!/bin/bash

appName=$(pwd |awk -F '/' '{print $NF}')
installHome=/opt/${appName}
configHome=${installHome}/conf

TOATL_MEM=`free -m | grep Mem | awk '{print $2}'`
MAX_HEAP_MEM=$(expr $TOATL_MEM \* 3 / 4)
MIN_HEAP_MEM=$MAX_HEAP_MEM
NEW_HEAP_MEM=$(expr $MAX_HEAP_MEM / 4)
MAX_META_MEM=$(expr $MAX_HEAP_MEM / 24)
MIN_META_MEM=$(expr $MAX_META_MEM / 24)
MAX_DIRECT_MEMORY=$(expr $MAX_META_MEM / 6)
MEM_OPS="-Xmx"$MAX_HEAP_MEM"M -Xms"$MIN_HEAP_MEM"M -Xmn"$NEW_HEAP_MEM"M -XX:MaxDirectMemorySize="$MAX_DIRECT_MEMORY"M -XX:MetaspaceSize="$MIN_META_MEM"M -XX:MaxMetaspaceSize="$MAX_META_MEM"M"
echo "MEM_OPS: $MEM_OPS"

VM_OPS="$MEM_OPS -Djava.rmi.server.hostname=127.0.0.1 -Dcom.sun.management.jmxremote=false -Dcom.sun.management.jmxremote.host=127.0.0.1 -Dcom.sun.management.jmxremote.port=1099 -Dcom.sun.management.jmxremote.ssl=false  -Dcom.sun.management.jmxremote.local.only=true -Dcom.sun.management.jmxremote.authenticate=false -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=${installHome}/logs/oom.hprof"
echo "VM_OPS: $VM_OPS"

JDK_JAVA_OPTIONS="
--add-exports java.base/sun.net.ftp=ALL-UNNAMED
--add-exports java.base/jdk.internal.reflect=ALL-UNNAMED
--add-exports java.base/jdk.internal.misc=ALL-UNNAMED
--add-exports java.base/sun.reflect.generics.reflectiveObjects=ALL-UNNAMED
--add-exports java.xml/com.sun.xml.internal.stream=ALL-UNNAMED
--add-opens jdk.compiler/com.sun.tools.javac.code=ALL-UNNAMED
--add-opens jdk.compiler/com.sun.tools.javac.comp=ALL-UNNAMED
--add-opens jdk.compiler/com.sun.tools.javac.file=ALL-UNNAMED
--add-opens jdk.compiler/com.sun.tools.javac.main=ALL-UNNAMED
--add-opens jdk.compiler/com.sun.tools.javac.model=ALL-UNNAMED
--add-opens jdk.compiler/com.sun.tools.javac.parser=ALL-UNNAMED
--add-opens jdk.compiler/com.sun.tools.javac.processing=ALL-UNNAMED
--add-opens jdk.compiler/com.sun.tools.javac.tree=ALL-UNNAMED
--add-opens jdk.compiler/com.sun.tools.javac.util=ALL-UNNAMED
--add-opens jdk.compiler/com.sun.tools.javac.jvm=ALL-UNNAMED
--add-opens java.base/java.lang=ALL-UNNAMED
--add-opens java.base/java.lang.invoke=ALL-UNNAMED
--add-opens java.base/java.math=ALL-UNNAMED
--add-opens java.base/java.net=ALL-UNNAMED
--add-opens java.base/java.text=ALL-UNNAMED
--add-opens java.base/java.util=ALL-UNNAMED
--add-opens java.base/java.util.concurrent=ALL-UNNAMED
--add-opens java.base/java.io=ALL-UNNAMED
--add-opens java.xml/com.sun.xml.internal.stream=ALL-UNNAMED
--add-opens java.base/jdk.internal.loader=ALL-UNNAMED
--add-opens java.base/java.lang.reflect=ALL-UNNAMED
--add-opens java.xml/com.sun.org.apache.xerces.internal.impl=ALL-UNNAMED
--add-opens java.base/jdk.internal.access.foreign=ALL-UNNAMED
--add-opens java.base/java.nio=ALL-UNNAMED
--add-opens java.base/java.time=ALL-UNNAMED"

JAVA_OPTS="$JDK_JAVA_OPTIONS -Dspring.profiles.active=prod -Dspring.config.location=${configHome}/ "$VM_OPS
echo "START_OPS: $JAVA_OPTS"

function start()
{
	count=`ps -ef |grep java|grep $appName|wc -l`
	if [ $count != 0 ];then
		echo "Maybe $appName is running, please check it..."
	else
		echo "The $appName is starting..."
		echo "java opts: $JAVA_OPTS"
    nohup java $JAVA_OPTS -jar bin/$appName.jar  > /dev/null 2>&1 &
	fi
}

function stop()
{
    echo "stopping $appName"
    i=1
    max_retry=1
    while [ $i -le $max_retry ]
    do
        pid=`ps -ef | grep java | grep "$appName" | grep -v grep | awk '{print $2}'`
        echo "existing pid: $pid"
        echo "try kill time: $i"
        if [ ! $pid ]; then
            break
        fi
        kill $pid
        i=`expr $i + 1`
        sleep 2s
    done
    pid=`ps -ef | grep java | grep "$appName" | grep -v grep | awk '{print $2}'`
    if [ $pid ]; then
        echo "trying kill -9 $appName"
        kill -9 $pid
    fi
    echo "$appName stopped!"
}

function restart()
{
    # get release version

    stop
    for i in {5..1}
    do
        echo -n "$i "
        sleep 1
    done
    echo 0

    backup

    start
}

function backup()
{
    # get backup version
    backupApp=`ls |grep -wv $releaseApp$ |grep .jar$`

    # create backup dir
    if [ ! -d "backup" ];then
        mkdir backup
    fi

    # backup
    for i in ${backupApp[@]}
    do
        echo "backup" $i
        mv $i backup
    done
}

function status()
{
    appId=`ps -ef |grep java|grep $appName|awk '{print $2}'`
	if [ -z $appId ]
	then
	    echo -e "\033[31m Not running \033[0m"
	else
	    echo -e "\033[32m Running [$appId] \033[0m"
	fi
}


function usage()
{
    echo "Usage: $0 {start|stop|restart|status|stop -f}"
    echo "Example: $0 start"
    exit 1
}

case $1 in
	start)
	start;;

	stop)
	stop;;

	restart)
	restart;;

	status)
	status;;

	*)
	usage;;
esac
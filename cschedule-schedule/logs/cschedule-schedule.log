2025-04-22 09:17:03.538[main][INFO][CSMSUtil] - Find CSMS SDK config file in class path csms.properties
2025-04-22 09:17:03.540[main][INFO][CSMSBeanFactory] - add CSMS shutdown hook
2025-04-22 09:17:03.565[main][INFO][CSMSServiceProvider] - CSMS SDK config {"isEnable":true,"rotateConfig":{"enable":true,"pollingInterval":60,"maxThreads":20,"maxQueueSize":20,"maxWait":300},"metricConfig":{"enable":true,"reportInterval":1800,"hotReloadReportEnable":true,"hotReloadReportInterval":240},"endpoints":"https://csmsdev.zoomdev.us","path":"dev/cube","durationSeconds":3600,"sectionName":"default","httpOptions":{"connectTimeout":5000,"readTimeout":10000,"callTimeout":15000,"maxRetry":3,"maxIdleConnections":20,"keepAliveDuration":5,"retrySleepMillSecond":200},"isEnableIMDSv2":true,"maxRegisterPublicPathsCount":2000,"verifyPublicPath":true}
2025-04-22 09:17:03.577[main][INFO][CSMSMetricServiceImpl] - Start report metric task successful
2025-04-22 09:17:03.591[main][ERROR][AuthServiceImpl] - Try use IMDSv2 get token with error Host is down, will try to use IMSDv1
2025-04-22 09:17:03.592[main][WARN][AuthServiceImpl] - Get instance bind role failed with error Host is down
2025-04-22 09:17:03.592[main][INFO][AuthServiceImpl] - Try to check local aws credentials file to judge deploy type
2025-04-22 09:17:03.593[main][INFO][AuthServiceImpl] - Judge EC2 deploy type. Inner deploy type=EC2
2025-04-22 09:17:03.594[main][INFO][SystemUtil] - Get process pid 77424
2025-04-22 09:17:03.596[main][WARN][SystemUtil] - Get SN from CMDB error Connection refused
2025-04-22 09:17:03.596[main][WARN][SystemUtil] - Get instanceId from CMDB error Connection refused
2025-04-22 09:17:03.597[main][WARN][SystemUtil] - Get process CMD from CMDB error Connection refused
2025-04-22 09:17:03.597[main][INFO][SystemUtil] - Get AppProcessId:403e90b0-411e-48ca-8e52-f1f730804e30
2025-04-22 09:17:03.597[main][INFO][AuthServiceImpl] - Init refresh token task successful
2025-04-22 09:17:03.598[main][ERROR][AuthServiceImpl] - Try use IMDSv2 get token with error Host is down, will try to use IMSDv1
2025-04-22 09:17:03.600[main][ERROR][CSMSApplicationContextInitializer] - Get secrets from CSMS server failed
us.zoom.cloud.secrets.exception.CSMSCredException: Get EC2 STS failed: Host is down
	at us.zoom.cloud.secrets.api.impl.AuthServiceImpl.getCredKey(AuthServiceImpl.java:178)
	at us.zoom.cloud.secrets.api.impl.AuthServiceImpl.getCredToken(AuthServiceImpl.java:155)
	at us.zoom.cloud.secrets.api.impl.AuthServiceImpl.getToken(AuthServiceImpl.java:109)
	at us.zoom.cloud.secrets.api.impl.BaseRestApi.withRetryReq(BaseRestApi.java:142)
	at us.zoom.cloud.secrets.api.impl.BaseRestApi.withRetryReq(BaseRestApi.java:125)
	at us.zoom.cloud.secrets.api.impl.CSMSRestApiImpl.withRetryQuery(CSMSRestApiImpl.java:266)
	at us.zoom.cloud.secrets.api.impl.CSMSRestApiImpl.get(CSMSRestApiImpl.java:45)
	at us.zoom.cloud.secrets.service.impl.BaseCSMSService.getByPath(BaseCSMSService.java:82)
	at us.zoom.cloud.secrets.service.impl.CSMSServiceImpl.getAll(CSMSServiceImpl.java:32)
	at us.zoom.cloud.secrets.service.impl.CSMSServiceImpl.getAll(CSMSServiceImpl.java:23)
	at us.zoom.cloud.secrets.env.springboot.CSMSApplicationContextInitializer.initialize(CSMSApplicationContextInitializer.java:30)
	at org.springframework.boot.SpringApplication.applyInitializers(SpringApplication.java:612)
	at org.springframework.boot.SpringApplication.prepareContext(SpringApplication.java:383)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:317)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1361)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1350)
	at us.zoom.cschedule.schedule.CscheduleScheduleApplication.main(CscheduleScheduleApplication.java:21)
2025-04-22 09:17:03.601[refreshToken][ERROR][AuthServiceImpl] - Try use IMDSv2 get token with error Host is down, will try to use IMSDv1
2025-04-22 09:17:03.602[refreshToken][ERROR][AuthServiceImpl$RefreshTokenTask] - Get EC2 STS error Host is down
2025-04-22 09:17:03.604[refreshToken][ERROR][AuthServiceImpl] - Try use IMDSv2 get token with error Host is down, will try to use IMSDv1
2025-04-22 09:17:03.605[refreshToken][WARN][AuthServiceImpl$RefreshTokenTask] - Refresh JWT error Get EC2 STS failed: Host is down
2025-04-22 09:17:03.605[refreshToken][INFO][AuthServiceImpl$RefreshTokenTask] - Refresh JWT elapsed 2ms
2025-04-22 09:17:04.247[main][INFO][CSMSMultiServiceBridgeInitializer] - Init CSMSMultiServiceBridge successful
2025-04-22 09:17:04.248[main][WARN][CSMSMergeApplicationContextInitializer] - Ignore merge operate, because not find csmsPropertyResource
2025-04-22 09:17:04.249[main][INFO][CscheduleScheduleApplication] - Starting CscheduleScheduleApplication using Java 17.0.12 with PID 77424 (/Users/<USER>/IdeaGitProjects/cube-scheduler/cschedule-schedule/target/classes started by StarlsDing in /Users/<USER>/IdeaGitProjects/cube-scheduler)
2025-04-22 09:17:04.250[main][INFO][CscheduleScheduleApplication] - No active profile set, falling back to 1 default profile: "default"
2025-04-22 09:17:05.503[main][INFO][RotateHandlerStore] - Register secret keys [cube.dataSource.username, cube.dataSource.password] with HikariCPHotRotateHandler CurrentVersionHandler
2025-04-22 09:17:05.504[main][INFO][CSMSHotReloadMetricServiceImpl] - Start report hot reload metric task successful
2025-04-22 09:17:05.504[main][INFO][CSMSHotReloadMetricServiceImpl] - Add hot reload metrics secret cube.dataSource.username to map.
2025-04-22 09:17:05.504[main][INFO][CSMSHotReloadMetricServiceImpl] - Add hot reload metrics secret cube.dataSource.password to map.
2025-04-22 09:17:05.504[main][INFO][HikariDataSource] - [mysql:rotation] the datasource name[cube-scheduler-datasource] ,these keys [cube.dataSource.username,cube.dataSource.password] will be monitored and support hot reload
2025-04-22 09:17:05.506[main][INFO][RotateHandlerStore] - Register secret keys [cube.dataSource.username, cube.dataSource.password] with HikariCPHotRotateHandler CurrentVersionHandler
2025-04-22 09:17:05.507[main][INFO][HikariDataSource] - [mysql:rotation] the datasource name[cube-scheduler-datasource] ,these keys [cube.dataSource.username,cube.dataSource.password] will be monitored and support hot reload
2025-04-22 09:17:05.525[main][INFO][MultipleSchedulerConfig] - load quartz properties from classpath successfully, properties: {org.quartz.scheduler.batchTriggerAcquisitionMaxCount=100, org.quartz.scheduler.instanceId=AUTO, org.quartz.threadPool.threadCount=100, org.quartz.jobStore.acquireTriggersWithinLock=true, org.quartz.scheduler.instanceName=quartzScheduler, org.quartz.jobStore.lockHandler.class=org.quartz.impl.jdbcjobstore.SimpleSemaphore, org.quartz.jobStore.tablePrefix=QRTZ_, org.quartz.scheduler.batchTriggerAcquisitionFireAheadTimeWindow=10000}
2025-04-22 09:17:05.545[main][INFO][StdSchedulerFactory] - Using custom data access locking (synchronization): org.quartz.impl.jdbcjobstore.SimpleSemaphore
2025-04-22 09:17:05.545[main][INFO][StdSchedulerFactory] - Using default implementation for ThreadExecutor
2025-04-22 09:17:05.556[main][INFO][SchedulerSignalerImpl] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-04-22 09:17:05.556[main][INFO][QuartzScheduler] - Quartz Scheduler v.2.3.2 created.
2025-04-22 09:17:05.561[main][ERROR][HikariConfig] - cube-scheduler-datasource - dataSource or dataSourceClassName or jdbcUrl is required.
2025-04-22 09:17:05.561[main][INFO][QuartzScheduler] - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
2025-04-22 09:17:05.561[main][INFO][QuartzScheduler] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-04-22 09:17:05.561[main][DEBUG][SimpleThreadPool] - Shutting down threadpool...
2025-04-22 09:17:05.561[main][DEBUG][SimpleThreadPool] - Shutdown of threadpool complete.
2025-04-22 09:17:05.561[main][INFO][QuartzScheduler] - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
2025-04-22 09:17:06.050[quartzScheduler_Worker-14][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:17:06.050[quartzScheduler_Worker-20][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:17:06.050[quartzScheduler_Worker-33][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:17:06.050[quartzScheduler_Worker-32][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:17:06.050[quartzScheduler_Worker-64][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:17:06.049[quartzScheduler_Worker-1][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:17:06.050[quartzScheduler_Worker-63][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:17:06.050[quartzScheduler_Worker-27][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:17:06.050[quartzScheduler_Worker-44][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:17:06.050[quartzScheduler_Worker-22][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:17:06.050[quartzScheduler_Worker-37][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:17:06.050[quartzScheduler_Worker-40][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:17:06.050[quartzScheduler_Worker-28][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:17:06.050[quartzScheduler_Worker-35][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:17:06.050[quartzScheduler_Worker-16][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:17:06.051[quartzScheduler_Worker-13][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:17:06.051[quartzScheduler_Worker-18][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:17:06.051[quartzScheduler_Worker-9][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:17:06.049[quartzScheduler_Worker-15][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:17:06.050[quartzScheduler_Worker-39][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:17:06.050[quartzScheduler_Worker-49][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:17:06.050[quartzScheduler_Worker-17][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:17:06.049[quartzScheduler_Worker-5][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:17:06.050[quartzScheduler_Worker-62][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:17:06.051[quartzScheduler_Worker-56][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:17:06.051[quartzScheduler_Worker-2][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:17:06.052[quartzScheduler_Worker-30][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:17:06.052[quartzScheduler_Worker-24][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:17:06.052[quartzScheduler_Worker-31][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:17:06.052[quartzScheduler_Worker-51][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:17:06.052[quartzScheduler_Worker-41][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:17:06.052[quartzScheduler_Worker-53][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:17:06.052[quartzScheduler_Worker-71][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:17:06.052[quartzScheduler_Worker-55][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:17:06.051[quartzScheduler_Worker-65][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:17:06.052[quartzScheduler_Worker-79][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:17:06.052[quartzScheduler_Worker-25][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:17:06.052[quartzScheduler_Worker-4][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:17:06.052[quartzScheduler_Worker-6][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:17:06.052[quartzScheduler_Worker-8][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:17:06.051[quartzScheduler_Worker-61][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:17:06.051[quartzScheduler_Worker-7][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:17:06.056[quartzScheduler_Worker-82][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:17:06.056[quartzScheduler_Worker-96][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:17:06.056[quartzScheduler_Worker-95][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:17:06.051[quartzScheduler_Worker-19][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:17:06.057[quartzScheduler_Worker-36][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:17:06.052[quartzScheduler_Worker-54][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:17:06.052[quartzScheduler_Worker-76][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:17:06.056[quartzScheduler_Worker-34][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:17:06.058[quartzScheduler_Worker-10][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:17:06.058[quartzScheduler_Worker-3][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:17:06.058[quartzScheduler_Worker-26][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:17:06.058[quartzScheduler_Worker-11][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:17:06.058[quartzScheduler_Worker-29][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:17:06.058[quartzScheduler_Worker-12][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:17:06.058[quartzScheduler_Worker-73][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:17:06.058[quartzScheduler_Worker-80][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:17:06.058[quartzScheduler_Worker-94][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:17:06.058[quartzScheduler_Worker-74][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:17:06.058[quartzScheduler_Worker-99][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:17:06.058[quartzScheduler_Worker-98][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:17:06.058[quartzScheduler_Worker-47][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:17:06.058[quartzScheduler_Worker-21][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:17:06.058[quartzScheduler_Worker-23][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:17:06.058[quartzScheduler_Worker-48][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:17:06.059[quartzScheduler_Worker-38][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:17:06.059[quartzScheduler_Worker-59][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:17:06.059[quartzScheduler_Worker-78][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:17:06.059[quartzScheduler_Worker-81][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:17:06.059[quartzScheduler_Worker-88][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:17:06.059[quartzScheduler_Worker-84][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:17:06.059[quartzScheduler_Worker-68][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:17:06.059[quartzScheduler_Worker-42][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:17:06.059[quartzScheduler_Worker-70][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:17:06.059[quartzScheduler_Worker-43][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:17:06.059[quartzScheduler_Worker-45][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:17:06.059[quartzScheduler_Worker-91][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:17:06.059[quartzScheduler_Worker-46][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:17:06.059[quartzScheduler_Worker-52][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:17:06.059[quartzScheduler_Worker-100][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:17:06.059[quartzScheduler_Worker-50][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:17:06.059[quartzScheduler_Worker-97][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:17:06.059[quartzScheduler_Worker-57][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:17:06.059[quartzScheduler_Worker-66][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:17:06.060[quartzScheduler_Worker-69][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:17:06.060[quartzScheduler_Worker-60][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:17:06.060[quartzScheduler_Worker-58][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:17:06.060[quartzScheduler_Worker-72][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:17:06.060[quartzScheduler_Worker-92][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:17:06.060[quartzScheduler_Worker-67][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:17:06.060[quartzScheduler_Worker-93][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:17:06.060[quartzScheduler_Worker-86][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:17:06.060[quartzScheduler_Worker-85][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:17:06.060[quartzScheduler_Worker-89][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:17:06.060[quartzScheduler_Worker-90][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:17:06.060[quartzScheduler_Worker-83][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:17:06.060[quartzScheduler_Worker-75][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:17:06.060[quartzScheduler_Worker-87][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:17:06.060[quartzScheduler_Worker-77][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:17:06.512[CSMS thread shutdown][INFO][CSMSBeanFactory] - start execute CSMS shutdown
2025-04-22 09:22:22.276[main][INFO][CSMSUtil] - Find CSMS SDK config file in class path csms.properties
2025-04-22 09:22:22.278[main][INFO][CSMSBeanFactory] - add CSMS shutdown hook
2025-04-22 09:22:22.305[main][INFO][CSMSServiceProvider] - CSMS SDK config {"isEnable":true,"rotateConfig":{"enable":true,"pollingInterval":60,"maxThreads":20,"maxQueueSize":20,"maxWait":300},"metricConfig":{"enable":true,"reportInterval":1800,"hotReloadReportEnable":true,"hotReloadReportInterval":240},"endpoints":"https://csmsdev.zoomdev.us","path":"dev/cube","durationSeconds":3600,"sectionName":"default","httpOptions":{"connectTimeout":5000,"readTimeout":10000,"callTimeout":15000,"maxRetry":3,"maxIdleConnections":20,"keepAliveDuration":5,"retrySleepMillSecond":200},"isEnableIMDSv2":true,"maxRegisterPublicPathsCount":2000,"verifyPublicPath":true}
2025-04-22 09:22:22.317[main][INFO][CSMSMetricServiceImpl] - Start report metric task successful
2025-04-22 09:22:24.333[main][ERROR][AuthServiceImpl] - Try use IMDSv2 get token with error Connect timed out, will try to use IMSDv1
2025-04-22 09:22:26.338[main][WARN][AuthServiceImpl] - Get instance bind role failed with error Connect timed out
2025-04-22 09:22:26.341[main][INFO][AuthServiceImpl] - Try to check local aws credentials file to judge deploy type
2025-04-22 09:22:26.344[main][INFO][AuthServiceImpl] - Judge EC2 deploy type. Inner deploy type=EC2
2025-04-22 09:22:26.348[main][INFO][SystemUtil] - Get process pid 77970
2025-04-22 09:22:26.349[main][WARN][SystemUtil] - Get SN from CMDB error Connection refused
2025-04-22 09:22:26.350[main][WARN][SystemUtil] - Get instanceId from CMDB error Connection refused
2025-04-22 09:22:26.350[main][WARN][SystemUtil] - Get process CMD from CMDB error Connection refused
2025-04-22 09:22:26.351[main][INFO][SystemUtil] - Get AppProcessId:58fe23d7-4284-49fb-a0d9-2a0bb3f92f14
2025-04-22 09:22:26.351[main][INFO][AuthServiceImpl] - Init refresh token task successful
2025-04-22 09:22:28.356[main][ERROR][AuthServiceImpl] - Try use IMDSv2 get token with error Connect timed out, will try to use IMSDv1
2025-04-22 09:22:28.361[refreshToken][ERROR][AuthServiceImpl] - Try use IMDSv2 get token with error Host is down, will try to use IMSDv1
2025-04-22 09:22:28.360[main][ERROR][CSMSApplicationContextInitializer] - Get secrets from CSMS server failed
us.zoom.cloud.secrets.exception.CSMSCredException: Get EC2 STS failed: Host is down
	at us.zoom.cloud.secrets.api.impl.AuthServiceImpl.getCredKey(AuthServiceImpl.java:178)
	at us.zoom.cloud.secrets.api.impl.AuthServiceImpl.getCredToken(AuthServiceImpl.java:155)
	at us.zoom.cloud.secrets.api.impl.AuthServiceImpl.getToken(AuthServiceImpl.java:109)
	at us.zoom.cloud.secrets.api.impl.BaseRestApi.withRetryReq(BaseRestApi.java:142)
	at us.zoom.cloud.secrets.api.impl.BaseRestApi.withRetryReq(BaseRestApi.java:125)
	at us.zoom.cloud.secrets.api.impl.CSMSRestApiImpl.withRetryQuery(CSMSRestApiImpl.java:266)
	at us.zoom.cloud.secrets.api.impl.CSMSRestApiImpl.get(CSMSRestApiImpl.java:45)
	at us.zoom.cloud.secrets.service.impl.BaseCSMSService.getByPath(BaseCSMSService.java:82)
	at us.zoom.cloud.secrets.service.impl.CSMSServiceImpl.getAll(CSMSServiceImpl.java:32)
	at us.zoom.cloud.secrets.service.impl.CSMSServiceImpl.getAll(CSMSServiceImpl.java:23)
	at us.zoom.cloud.secrets.env.springboot.CSMSApplicationContextInitializer.initialize(CSMSApplicationContextInitializer.java:30)
	at org.springframework.boot.SpringApplication.applyInitializers(SpringApplication.java:612)
	at org.springframework.boot.SpringApplication.prepareContext(SpringApplication.java:383)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:317)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1361)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1350)
	at us.zoom.cschedule.schedule.CscheduleScheduleApplication.main(CscheduleScheduleApplication.java:21)
2025-04-22 09:22:28.364[refreshToken][ERROR][AuthServiceImpl$RefreshTokenTask] - Get EC2 STS error Host is down
2025-04-22 09:22:28.367[refreshToken][ERROR][AuthServiceImpl] - Try use IMDSv2 get token with error Host is down, will try to use IMSDv1
2025-04-22 09:22:28.369[refreshToken][WARN][AuthServiceImpl$RefreshTokenTask] - Refresh JWT error Get EC2 STS failed: Host is down
2025-04-22 09:22:28.369[refreshToken][INFO][AuthServiceImpl$RefreshTokenTask] - Refresh JWT elapsed 5ms
2025-04-22 09:22:28.974[main][INFO][CSMSMultiServiceBridgeInitializer] - Init CSMSMultiServiceBridge successful
2025-04-22 09:22:28.975[main][WARN][CSMSMergeApplicationContextInitializer] - Ignore merge operate, because not find csmsPropertyResource
2025-04-22 09:22:28.976[main][INFO][CscheduleScheduleApplication] - Starting CscheduleScheduleApplication using Java 17.0.12 with PID 77970 (/Users/<USER>/IdeaGitProjects/cube-scheduler/cschedule-schedule/target/classes started by StarlsDing in /Users/<USER>/IdeaGitProjects/cube-scheduler)
2025-04-22 09:22:28.977[main][INFO][CscheduleScheduleApplication] - No active profile set, falling back to 1 default profile: "default"
2025-04-22 09:22:30.220[main][INFO][RotateHandlerStore] - Register secret keys [cube.dataSource.username, cube.dataSource.password] with HikariCPHotRotateHandler CurrentVersionHandler
2025-04-22 09:22:30.221[main][INFO][CSMSHotReloadMetricServiceImpl] - Start report hot reload metric task successful
2025-04-22 09:22:30.222[main][INFO][CSMSHotReloadMetricServiceImpl] - Add hot reload metrics secret cube.dataSource.username to map.
2025-04-22 09:22:30.222[main][INFO][CSMSHotReloadMetricServiceImpl] - Add hot reload metrics secret cube.dataSource.password to map.
2025-04-22 09:22:30.222[main][INFO][HikariDataSource] - [mysql:rotation] the datasource name[cube-scheduler-datasource] ,these keys [cube.dataSource.username,cube.dataSource.password] will be monitored and support hot reload
2025-04-22 09:22:30.224[main][INFO][RotateHandlerStore] - Register secret keys [cube.dataSource.username, cube.dataSource.password] with HikariCPHotRotateHandler CurrentVersionHandler
2025-04-22 09:22:30.224[main][INFO][HikariDataSource] - [mysql:rotation] the datasource name[cube-scheduler-datasource] ,these keys [cube.dataSource.username,cube.dataSource.password] will be monitored and support hot reload
2025-04-22 09:22:30.241[main][INFO][MultipleSchedulerConfig] - load quartz properties from classpath successfully, properties: {org.quartz.scheduler.batchTriggerAcquisitionMaxCount=100, org.quartz.scheduler.instanceId=AUTO, org.quartz.threadPool.threadCount=100, org.quartz.jobStore.acquireTriggersWithinLock=true, org.quartz.scheduler.instanceName=quartzScheduler, org.quartz.jobStore.lockHandler.class=org.quartz.impl.jdbcjobstore.SimpleSemaphore, org.quartz.jobStore.tablePrefix=QRTZ_, org.quartz.scheduler.batchTriggerAcquisitionFireAheadTimeWindow=10000}
2025-04-22 09:22:30.257[main][INFO][StdSchedulerFactory] - Using custom data access locking (synchronization): org.quartz.impl.jdbcjobstore.SimpleSemaphore
2025-04-22 09:22:30.257[main][INFO][StdSchedulerFactory] - Using default implementation for ThreadExecutor
2025-04-22 09:22:30.269[main][INFO][SchedulerSignalerImpl] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-04-22 09:22:30.269[main][INFO][QuartzScheduler] - Quartz Scheduler v.2.3.2 created.
2025-04-22 09:22:30.273[main][ERROR][HikariConfig] - cube-scheduler-datasource - dataSource or dataSourceClassName or jdbcUrl is required.
2025-04-22 09:22:30.273[main][INFO][QuartzScheduler] - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
2025-04-22 09:22:30.273[main][INFO][QuartzScheduler] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-04-22 09:22:30.273[main][DEBUG][SimpleThreadPool] - Shutting down threadpool...
2025-04-22 09:22:30.273[main][DEBUG][SimpleThreadPool] - Shutdown of threadpool complete.
2025-04-22 09:22:30.273[main][INFO][QuartzScheduler] - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
2025-04-22 09:22:30.761[quartzScheduler_Worker-23][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:22:30.763[quartzScheduler_Worker-64][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:22:30.763[quartzScheduler_Worker-1][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:22:30.763[quartzScheduler_Worker-3][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:22:30.763[quartzScheduler_Worker-75][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:22:30.763[quartzScheduler_Worker-76][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:22:30.763[quartzScheduler_Worker-14][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:22:30.763[quartzScheduler_Worker-86][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:22:30.763[quartzScheduler_Worker-81][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:22:30.763[quartzScheduler_Worker-70][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:22:30.763[quartzScheduler_Worker-79][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:22:30.763[quartzScheduler_Worker-16][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:22:30.764[quartzScheduler_Worker-2][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:22:30.764[quartzScheduler_Worker-7][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:22:30.764[quartzScheduler_Worker-33][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:22:30.764[quartzScheduler_Worker-41][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:22:30.764[quartzScheduler_Worker-54][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:22:30.764[quartzScheduler_Worker-55][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:22:30.764[quartzScheduler_Worker-57][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:22:30.764[quartzScheduler_Worker-80][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:22:30.764[quartzScheduler_Worker-84][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:22:30.764[quartzScheduler_Worker-87][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:22:30.764[quartzScheduler_Worker-88][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:22:30.764[quartzScheduler_Worker-21][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:22:30.764[quartzScheduler_Worker-5][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:22:30.764[quartzScheduler_Worker-31][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:22:30.764[quartzScheduler_Worker-29][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:22:30.764[quartzScheduler_Worker-26][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:22:30.764[quartzScheduler_Worker-35][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:22:30.764[quartzScheduler_Worker-83][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:22:30.764[quartzScheduler_Worker-85][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:22:30.764[quartzScheduler_Worker-45][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:22:30.764[quartzScheduler_Worker-63][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:22:30.764[quartzScheduler_Worker-4][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:22:30.764[quartzScheduler_Worker-6][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:22:30.764[quartzScheduler_Worker-12][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:22:30.765[quartzScheduler_Worker-15][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:22:30.765[quartzScheduler_Worker-18][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:22:30.765[quartzScheduler_Worker-22][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:22:30.765[quartzScheduler_Worker-30][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:22:30.765[quartzScheduler_Worker-37][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:22:30.765[quartzScheduler_Worker-42][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:22:30.765[quartzScheduler_Worker-39][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:22:30.765[quartzScheduler_Worker-52][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:22:30.765[quartzScheduler_Worker-53][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:22:30.765[quartzScheduler_Worker-65][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:22:30.764[quartzScheduler_Worker-24][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:22:30.765[quartzScheduler_Worker-77][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:22:30.765[quartzScheduler_Worker-91][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:22:30.764[quartzScheduler_Worker-71][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:22:30.765[quartzScheduler_Worker-8][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:22:30.765[quartzScheduler_Worker-25][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:22:30.765[quartzScheduler_Worker-50][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:22:30.765[quartzScheduler_Worker-51][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:22:30.765[quartzScheduler_Worker-59][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:22:30.765[quartzScheduler_Worker-62][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:22:30.765[quartzScheduler_Worker-69][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:22:30.765[quartzScheduler_Worker-73][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:22:30.765[quartzScheduler_Worker-47][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:22:30.765[quartzScheduler_Worker-20][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:22:30.765[quartzScheduler_Worker-32][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:22:30.764[quartzScheduler_Worker-19][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:22:30.764[quartzScheduler_Worker-34][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:22:30.764[quartzScheduler_Worker-72][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:22:30.768[quartzScheduler_Worker-40][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:22:30.768[quartzScheduler_Worker-27][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:22:30.768[quartzScheduler_Worker-10][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:22:30.765[quartzScheduler_Worker-36][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:22:30.765[quartzScheduler_Worker-38][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:22:30.768[quartzScheduler_Worker-28][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:22:30.765[quartzScheduler_Worker-17][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:22:30.768[quartzScheduler_Worker-67][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:22:30.768[quartzScheduler_Worker-13][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:22:30.769[quartzScheduler_Worker-48][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:22:30.768[quartzScheduler_Worker-44][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:22:30.769[quartzScheduler_Worker-58][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:22:30.765[quartzScheduler_Worker-97][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:22:30.765[quartzScheduler_Worker-43][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:22:30.769[quartzScheduler_Worker-56][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:22:30.769[quartzScheduler_Worker-74][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:22:30.768[quartzScheduler_Worker-95][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:22:30.768[quartzScheduler_Worker-66][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:22:30.769[quartzScheduler_Worker-94][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:22:30.768[quartzScheduler_Worker-89][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:22:30.768[quartzScheduler_Worker-60][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:22:30.768[quartzScheduler_Worker-99][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:22:30.769[quartzScheduler_Worker-90][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:22:30.769[quartzScheduler_Worker-78][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:22:30.768[quartzScheduler_Worker-92][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:22:30.765[quartzScheduler_Worker-11][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:22:30.769[quartzScheduler_Worker-96][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:22:30.768[quartzScheduler_Worker-68][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:22:30.765[quartzScheduler_Worker-82][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:22:30.765[quartzScheduler_Worker-9][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:22:30.769[quartzScheduler_Worker-98][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:22:30.769[quartzScheduler_Worker-93][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:22:30.769[quartzScheduler_Worker-49][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:22:30.769[quartzScheduler_Worker-46][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:22:30.769[quartzScheduler_Worker-100][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:22:30.770[quartzScheduler_Worker-61][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:22:32.357[CSMS thread shutdown][INFO][CSMSBeanFactory] - start execute CSMS shutdown
2025-04-22 09:37:15.931[main][INFO][CSMSUtil] - Find CSMS SDK config file in class path csms.properties
2025-04-22 09:37:15.933[main][INFO][CSMSBeanFactory] - add CSMS shutdown hook
2025-04-22 09:37:15.962[main][INFO][CSMSServiceProvider] - CSMS SDK config {"isEnable":true,"rotateConfig":{"enable":true,"pollingInterval":60,"maxThreads":20,"maxQueueSize":20,"maxWait":300},"metricConfig":{"enable":true,"reportInterval":1800,"hotReloadReportEnable":true,"hotReloadReportInterval":240},"endpoints":"https://csmsdev.zoomdev.us","path":"dev/cube","durationSeconds":3600,"sectionName":"default","httpOptions":{"connectTimeout":5000,"readTimeout":10000,"callTimeout":15000,"maxRetry":3,"maxIdleConnections":20,"keepAliveDuration":5,"retrySleepMillSecond":200},"isEnableIMDSv2":true,"maxRegisterPublicPathsCount":2000,"verifyPublicPath":true}
2025-04-22 09:37:15.975[main][INFO][CSMSMetricServiceImpl] - Start report metric task successful
2025-04-22 09:37:17.994[main][ERROR][AuthServiceImpl] - Try use IMDSv2 get token with error Connect timed out, will try to use IMSDv1
2025-04-22 09:37:17.995[main][WARN][AuthServiceImpl] - Get instance bind role failed with error Host is down
2025-04-22 09:37:17.995[main][INFO][AuthServiceImpl] - Try to check local aws credentials file to judge deploy type
2025-04-22 09:37:17.996[main][INFO][AuthServiceImpl] - Judge EC2 deploy type. Inner deploy type=EC2
2025-04-22 09:37:17.998[main][INFO][SystemUtil] - Get process pid 79491
2025-04-22 09:37:17.999[main][WARN][SystemUtil] - Get SN from CMDB error Connection refused
2025-04-22 09:37:17.999[main][WARN][SystemUtil] - Get instanceId from CMDB error Connection refused
2025-04-22 09:37:17.999[main][WARN][SystemUtil] - Get process CMD from CMDB error Connection refused
2025-04-22 09:37:17.999[main][INFO][SystemUtil] - Get AppProcessId:a396e253-e82a-44e7-9a57-85eef01c4c0b
2025-04-22 09:37:18.000[main][INFO][AuthServiceImpl] - Init refresh token task successful
2025-04-22 09:37:18.001[main][ERROR][AuthServiceImpl] - Try use IMDSv2 get token with error Host is down, will try to use IMSDv1
2025-04-22 09:37:18.002[main][ERROR][CSMSApplicationContextInitializer] - Get secrets from CSMS server failed
us.zoom.cloud.secrets.exception.CSMSCredException: Get EC2 STS failed: Host is down
	at us.zoom.cloud.secrets.api.impl.AuthServiceImpl.getCredKey(AuthServiceImpl.java:178)
	at us.zoom.cloud.secrets.api.impl.AuthServiceImpl.getCredToken(AuthServiceImpl.java:155)
	at us.zoom.cloud.secrets.api.impl.AuthServiceImpl.getToken(AuthServiceImpl.java:109)
	at us.zoom.cloud.secrets.api.impl.BaseRestApi.withRetryReq(BaseRestApi.java:142)
	at us.zoom.cloud.secrets.api.impl.BaseRestApi.withRetryReq(BaseRestApi.java:125)
	at us.zoom.cloud.secrets.api.impl.CSMSRestApiImpl.withRetryQuery(CSMSRestApiImpl.java:266)
	at us.zoom.cloud.secrets.api.impl.CSMSRestApiImpl.get(CSMSRestApiImpl.java:45)
	at us.zoom.cloud.secrets.service.impl.BaseCSMSService.getByPath(BaseCSMSService.java:82)
	at us.zoom.cloud.secrets.service.impl.CSMSServiceImpl.getAll(CSMSServiceImpl.java:32)
	at us.zoom.cloud.secrets.service.impl.CSMSServiceImpl.getAll(CSMSServiceImpl.java:23)
	at us.zoom.cloud.secrets.env.springboot.CSMSApplicationContextInitializer.initialize(CSMSApplicationContextInitializer.java:30)
	at org.springframework.boot.SpringApplication.applyInitializers(SpringApplication.java:612)
	at org.springframework.boot.SpringApplication.prepareContext(SpringApplication.java:383)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:317)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1361)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1350)
	at us.zoom.cschedule.schedule.CscheduleScheduleApplication.main(CscheduleScheduleApplication.java:21)
2025-04-22 09:37:18.003[refreshToken][ERROR][AuthServiceImpl] - Try use IMDSv2 get token with error Host is down, will try to use IMSDv1
2025-04-22 09:37:18.004[refreshToken][ERROR][AuthServiceImpl$RefreshTokenTask] - Get EC2 STS error Host is down
2025-04-22 09:37:18.006[refreshToken][ERROR][AuthServiceImpl] - Try use IMDSv2 get token with error Host is down, will try to use IMSDv1
2025-04-22 09:37:18.006[refreshToken][WARN][AuthServiceImpl$RefreshTokenTask] - Refresh JWT error Get EC2 STS failed: Host is down
2025-04-22 09:37:18.007[refreshToken][INFO][AuthServiceImpl$RefreshTokenTask] - Refresh JWT elapsed 3ms
2025-04-22 09:37:18.601[main][INFO][CSMSMultiServiceBridgeInitializer] - Init CSMSMultiServiceBridge successful
2025-04-22 09:37:18.602[main][WARN][CSMSMergeApplicationContextInitializer] - Ignore merge operate, because not find csmsPropertyResource
2025-04-22 09:37:18.603[main][INFO][CscheduleScheduleApplication] - Starting CscheduleScheduleApplication using Java 17.0.12 with PID 79491 (/Users/<USER>/IdeaGitProjects/cube-scheduler/cschedule-schedule/target/classes started by StarlsDing in /Users/<USER>/IdeaGitProjects/cube-scheduler)
2025-04-22 09:37:18.604[main][INFO][CscheduleScheduleApplication] - No active profile set, falling back to 1 default profile: "default"
2025-04-22 09:37:19.879[main][INFO][RotateHandlerStore] - Register secret keys [cube.dataSource.username, cube.dataSource.password] with HikariCPHotRotateHandler CurrentVersionHandler
2025-04-22 09:37:19.880[main][INFO][CSMSHotReloadMetricServiceImpl] - Start report hot reload metric task successful
2025-04-22 09:37:19.880[main][INFO][CSMSHotReloadMetricServiceImpl] - Add hot reload metrics secret cube.dataSource.username to map.
2025-04-22 09:37:19.880[main][INFO][CSMSHotReloadMetricServiceImpl] - Add hot reload metrics secret cube.dataSource.password to map.
2025-04-22 09:37:19.880[main][INFO][HikariDataSource] - [mysql:rotation] the datasource name[cube-scheduler-datasource] ,these keys [cube.dataSource.username,cube.dataSource.password] will be monitored and support hot reload
2025-04-22 09:37:19.882[main][INFO][RotateHandlerStore] - Register secret keys [cube.dataSource.username, cube.dataSource.password] with HikariCPHotRotateHandler CurrentVersionHandler
2025-04-22 09:37:19.882[main][INFO][HikariDataSource] - [mysql:rotation] the datasource name[cube-scheduler-datasource] ,these keys [cube.dataSource.username,cube.dataSource.password] will be monitored and support hot reload
2025-04-22 09:37:19.899[main][INFO][MultipleSchedulerConfig] - load quartz properties from classpath successfully, properties: {org.quartz.scheduler.batchTriggerAcquisitionMaxCount=100, org.quartz.scheduler.instanceId=AUTO, org.quartz.threadPool.threadCount=100, org.quartz.jobStore.acquireTriggersWithinLock=true, org.quartz.scheduler.instanceName=quartzScheduler, org.quartz.jobStore.lockHandler.class=org.quartz.impl.jdbcjobstore.SimpleSemaphore, org.quartz.jobStore.tablePrefix=QRTZ_, org.quartz.scheduler.batchTriggerAcquisitionFireAheadTimeWindow=10000}
2025-04-22 09:37:19.916[main][INFO][StdSchedulerFactory] - Using custom data access locking (synchronization): org.quartz.impl.jdbcjobstore.SimpleSemaphore
2025-04-22 09:37:19.917[main][INFO][StdSchedulerFactory] - Using default implementation for ThreadExecutor
2025-04-22 09:37:19.926[main][INFO][SchedulerSignalerImpl] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-04-22 09:37:19.927[main][INFO][QuartzScheduler] - Quartz Scheduler v.2.3.2 created.
2025-04-22 09:37:19.931[main][ERROR][HikariConfig] - cube-scheduler-datasource - dataSource or dataSourceClassName or jdbcUrl is required.
2025-04-22 09:37:19.931[main][INFO][QuartzScheduler] - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
2025-04-22 09:37:19.931[main][INFO][QuartzScheduler] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-04-22 09:37:19.931[main][DEBUG][SimpleThreadPool] - Shutting down threadpool...
2025-04-22 09:37:19.931[main][DEBUG][SimpleThreadPool] - Shutdown of threadpool complete.
2025-04-22 09:37:19.931[main][INFO][QuartzScheduler] - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
2025-04-22 09:37:20.423[quartzScheduler_Worker-21][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:37:20.423[quartzScheduler_Worker-24][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:37:20.423[quartzScheduler_Worker-30][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:37:20.423[quartzScheduler_Worker-64][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:37:20.423[quartzScheduler_Worker-66][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:37:20.424[quartzScheduler_Worker-71][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:37:20.424[quartzScheduler_Worker-81][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:37:20.424[quartzScheduler_Worker-82][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:37:20.424[quartzScheduler_Worker-91][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:37:20.424[quartzScheduler_Worker-92][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:37:20.422[quartzScheduler_Worker-11][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:37:20.424[quartzScheduler_Worker-2][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:37:20.424[quartzScheduler_Worker-12][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:37:20.424[quartzScheduler_Worker-5][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:37:20.424[quartzScheduler_Worker-13][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:37:20.424[quartzScheduler_Worker-6][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:37:20.424[quartzScheduler_Worker-25][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:37:20.425[quartzScheduler_Worker-7][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:37:20.425[quartzScheduler_Worker-27][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:37:20.425[quartzScheduler_Worker-28][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:37:20.425[quartzScheduler_Worker-29][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:37:20.425[quartzScheduler_Worker-39][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:37:20.425[quartzScheduler_Worker-33][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:37:20.425[quartzScheduler_Worker-45][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:37:20.425[quartzScheduler_Worker-37][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:37:20.425[quartzScheduler_Worker-48][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:37:20.425[quartzScheduler_Worker-40][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:37:20.425[quartzScheduler_Worker-54][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:37:20.425[quartzScheduler_Worker-42][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:37:20.425[quartzScheduler_Worker-44][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:37:20.425[quartzScheduler_Worker-55][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:37:20.425[quartzScheduler_Worker-49][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:37:20.426[quartzScheduler_Worker-56][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:37:20.426[quartzScheduler_Worker-50][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:37:20.426[quartzScheduler_Worker-79][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:37:20.426[quartzScheduler_Worker-51][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:37:20.426[quartzScheduler_Worker-80][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:37:20.426[quartzScheduler_Worker-53][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:37:20.426[quartzScheduler_Worker-58][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:37:20.426[quartzScheduler_Worker-59][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:37:20.426[quartzScheduler_Worker-60][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:37:20.426[quartzScheduler_Worker-85][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:37:20.426[quartzScheduler_Worker-86][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:37:20.426[quartzScheduler_Worker-88][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:37:20.426[quartzScheduler_Worker-96][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:37:20.426[quartzScheduler_Worker-97][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:37:20.422[quartzScheduler_Worker-10][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:37:20.423[quartzScheduler_Worker-23][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:37:20.424[quartzScheduler_Worker-90][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:37:20.426[quartzScheduler_Worker-87][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:37:20.431[quartzScheduler_Worker-100][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:37:20.431[quartzScheduler_Worker-34][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:37:20.432[quartzScheduler_Worker-1][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:37:20.432[quartzScheduler_Worker-4][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:37:20.432[quartzScheduler_Worker-3][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:37:20.432[quartzScheduler_Worker-8][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:37:20.432[quartzScheduler_Worker-38][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:37:20.432[quartzScheduler_Worker-9][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:37:20.433[quartzScheduler_Worker-47][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:37:20.432[quartzScheduler_Worker-41][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:37:20.433[quartzScheduler_Worker-65][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:37:20.433[quartzScheduler_Worker-76][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:37:20.433[quartzScheduler_Worker-68][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:37:20.433[quartzScheduler_Worker-83][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:37:20.433[quartzScheduler_Worker-77][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:37:20.433[quartzScheduler_Worker-78][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:37:20.433[quartzScheduler_Worker-94][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:37:20.433[quartzScheduler_Worker-31][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:37:20.433[quartzScheduler_Worker-57][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:37:20.433[quartzScheduler_Worker-17][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:37:20.433[quartzScheduler_Worker-14][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:37:20.433[quartzScheduler_Worker-62][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:37:20.433[quartzScheduler_Worker-72][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:37:20.433[quartzScheduler_Worker-26][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:37:20.433[quartzScheduler_Worker-35][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:37:20.433[quartzScheduler_Worker-20][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:37:20.433[quartzScheduler_Worker-61][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:37:20.433[quartzScheduler_Worker-84][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:37:20.433[quartzScheduler_Worker-75][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:37:20.434[quartzScheduler_Worker-36][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:37:20.433[quartzScheduler_Worker-22][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:37:20.434[quartzScheduler_Worker-99][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:37:20.434[quartzScheduler_Worker-43][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:37:20.433[quartzScheduler_Worker-69][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:37:20.434[quartzScheduler_Worker-93][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:37:20.433[quartzScheduler_Worker-32][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:37:20.434[quartzScheduler_Worker-73][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:37:20.434[quartzScheduler_Worker-19][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:37:20.433[quartzScheduler_Worker-18][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:37:20.433[quartzScheduler_Worker-52][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:37:20.433[quartzScheduler_Worker-16][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:37:20.434[quartzScheduler_Worker-98][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:37:20.434[quartzScheduler_Worker-89][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:37:20.433[quartzScheduler_Worker-63][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:37:20.434[quartzScheduler_Worker-67][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:37:20.434[quartzScheduler_Worker-70][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:37:20.434[quartzScheduler_Worker-95][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:37:20.434[quartzScheduler_Worker-74][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:37:20.434[quartzScheduler_Worker-46][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:37:20.434[quartzScheduler_Worker-15][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:38:20.044[refreshToken][ERROR][AuthServiceImpl] - Try use IMDSv2 get token with error Connect timed out, will try to use IMSDv1
2025-04-22 09:38:22.050[refreshToken][ERROR][AuthServiceImpl$RefreshTokenTask] - Get EC2 STS error Connect timed out
2025-04-22 09:38:24.053[refreshToken][ERROR][AuthServiceImpl] - Try use IMDSv2 get token with error Connect timed out, will try to use IMSDv1
2025-04-22 09:38:24.061[refreshToken][WARN][AuthServiceImpl$RefreshTokenTask] - Refresh JWT error Get EC2 STS failed: Host is down
2025-04-22 09:38:24.062[refreshToken][INFO][AuthServiceImpl$RefreshTokenTask] - Refresh JWT elapsed 2012ms
2025-04-22 09:39:26.086[refreshToken][ERROR][AuthServiceImpl] - Try use IMDSv2 get token with error Connect timed out, will try to use IMSDv1
2025-04-22 09:39:26.099[refreshToken][ERROR][AuthServiceImpl$RefreshTokenTask] - Get EC2 STS error Host is down
2025-04-22 09:39:26.106[refreshToken][ERROR][AuthServiceImpl] - Try use IMDSv2 get token with error Host is down, will try to use IMSDv1
2025-04-22 09:39:26.113[refreshToken][WARN][AuthServiceImpl$RefreshTokenTask] - Refresh JWT error Get EC2 STS failed: Host is down
2025-04-22 09:39:26.114[refreshToken][INFO][AuthServiceImpl$RefreshTokenTask] - Refresh JWT elapsed 14ms
2025-04-22 09:40:28.125[refreshToken][ERROR][AuthServiceImpl] - Try use IMDSv2 get token with error Connect timed out, will try to use IMSDv1
2025-04-22 09:40:30.134[refreshToken][ERROR][AuthServiceImpl$RefreshTokenTask] - Get EC2 STS error Connect timed out
2025-04-22 09:40:32.137[refreshToken][ERROR][AuthServiceImpl] - Try use IMDSv2 get token with error Connect timed out, will try to use IMSDv1
2025-04-22 09:40:32.142[refreshToken][WARN][AuthServiceImpl$RefreshTokenTask] - Refresh JWT error Get EC2 STS failed: Host is down
2025-04-22 09:40:32.142[refreshToken][INFO][AuthServiceImpl$RefreshTokenTask] - Refresh JWT elapsed 2007ms
2025-04-22 09:41:19.896[reportHotReloadMetric][WARN][CSMSHotReloadMetricServiceImpl] - Can't get secrets cache for path dev/cube, cache keys: []
2025-04-22 09:41:34.147[refreshToken][ERROR][AuthServiceImpl] - Try use IMDSv2 get token with error Connect timed out, will try to use IMSDv1
2025-04-22 09:41:36.152[refreshToken][ERROR][AuthServiceImpl$RefreshTokenTask] - Get EC2 STS error Connect timed out
2025-04-22 09:41:38.154[refreshToken][ERROR][AuthServiceImpl] - Try use IMDSv2 get token with error Connect timed out, will try to use IMSDv1
2025-04-22 09:41:38.156[refreshToken][WARN][AuthServiceImpl$RefreshTokenTask] - Refresh JWT error Get EC2 STS failed: Host is down
2025-04-22 09:41:38.156[refreshToken][INFO][AuthServiceImpl$RefreshTokenTask] - Refresh JWT elapsed 2004ms
2025-04-22 09:42:40.173[refreshToken][ERROR][AuthServiceImpl] - Try use IMDSv2 get token with error Connect timed out, will try to use IMSDv1
2025-04-22 09:42:42.185[refreshToken][ERROR][AuthServiceImpl$RefreshTokenTask] - Get EC2 STS error Connect timed out
2025-04-22 09:42:44.190[refreshToken][ERROR][AuthServiceImpl] - Try use IMDSv2 get token with error Connect timed out, will try to use IMSDv1
2025-04-22 09:42:44.195[refreshToken][WARN][AuthServiceImpl$RefreshTokenTask] - Refresh JWT error Get EC2 STS failed: Host is down
2025-04-22 09:42:44.195[refreshToken][INFO][AuthServiceImpl$RefreshTokenTask] - Refresh JWT elapsed 2008ms
2025-04-22 09:43:46.223[refreshToken][ERROR][AuthServiceImpl] - Try use IMDSv2 get token with error Connect timed out, will try to use IMSDv1
2025-04-22 09:43:48.228[refreshToken][ERROR][AuthServiceImpl$RefreshTokenTask] - Get EC2 STS error Connect timed out
2025-04-22 09:43:50.231[refreshToken][ERROR][AuthServiceImpl] - Try use IMDSv2 get token with error Connect timed out, will try to use IMSDv1
2025-04-22 09:43:50.234[refreshToken][WARN][AuthServiceImpl$RefreshTokenTask] - Refresh JWT error Get EC2 STS failed: Host is down
2025-04-22 09:43:50.234[refreshToken][INFO][AuthServiceImpl$RefreshTokenTask] - Refresh JWT elapsed 2005ms
2025-04-22 09:44:52.244[refreshToken][ERROR][AuthServiceImpl] - Try use IMDSv2 get token with error Connect timed out, will try to use IMSDv1
2025-04-22 09:44:54.252[refreshToken][ERROR][AuthServiceImpl$RefreshTokenTask] - Get EC2 STS error Connect timed out
2025-04-22 09:44:56.258[refreshToken][ERROR][AuthServiceImpl] - Try use IMDSv2 get token with error Connect timed out, will try to use IMSDv1
2025-04-22 09:44:56.266[refreshToken][WARN][AuthServiceImpl$RefreshTokenTask] - Refresh JWT error Get EC2 STS failed: Host is down
2025-04-22 09:44:56.266[refreshToken][INFO][AuthServiceImpl$RefreshTokenTask] - Refresh JWT elapsed 2012ms
2025-04-22 09:45:19.902[reportHotReloadMetric][WARN][CSMSHotReloadMetricServiceImpl] - Can't get secrets cache for path dev/cube, cache keys: []
2025-04-22 09:45:58.283[refreshToken][ERROR][AuthServiceImpl] - Try use IMDSv2 get token with error Connect timed out, will try to use IMSDv1
2025-04-22 09:46:00.292[refreshToken][ERROR][AuthServiceImpl$RefreshTokenTask] - Get EC2 STS error Connect timed out
2025-04-22 09:46:02.221[CSMS thread shutdown][INFO][CSMSBeanFactory] - start execute CSMS shutdown
2025-04-22 09:46:06.065[main][INFO][CSMSUtil] - Find CSMS SDK config file in class path csms.properties
2025-04-22 09:46:06.067[main][INFO][CSMSBeanFactory] - add CSMS shutdown hook
2025-04-22 09:46:06.093[main][INFO][CSMSServiceProvider] - CSMS SDK config {"isEnable":true,"rotateConfig":{"enable":true,"pollingInterval":60,"maxThreads":20,"maxQueueSize":20,"maxWait":300},"metricConfig":{"enable":true,"reportInterval":1800,"hotReloadReportEnable":true,"hotReloadReportInterval":240},"endpoints":"https://csmsdev.zoomdev.us","path":"dev/cube","durationSeconds":3600,"sectionName":"default","httpOptions":{"connectTimeout":5000,"readTimeout":10000,"callTimeout":15000,"maxRetry":3,"maxIdleConnections":20,"keepAliveDuration":5,"retrySleepMillSecond":200},"isEnableIMDSv2":true,"maxRegisterPublicPathsCount":2000,"verifyPublicPath":true}
2025-04-22 09:46:06.105[main][INFO][CSMSMetricServiceImpl] - Start report metric task successful
2025-04-22 09:46:06.120[main][ERROR][AuthServiceImpl] - Try use IMDSv2 get token with error Host is down, will try to use IMSDv1
2025-04-22 09:46:06.121[main][WARN][AuthServiceImpl] - Get instance bind role failed with error Host is down
2025-04-22 09:46:06.121[main][INFO][AuthServiceImpl] - Try to check local aws credentials file to judge deploy type
2025-04-22 09:46:06.122[main][INFO][AuthServiceImpl] - Judge EC2 deploy type. Inner deploy type=EC2
2025-04-22 09:46:06.123[main][INFO][SystemUtil] - Get process pid 86491
2025-04-22 09:46:06.124[main][WARN][SystemUtil] - Get SN from CMDB error Connection refused
2025-04-22 09:46:06.125[main][WARN][SystemUtil] - Get instanceId from CMDB error Connection refused
2025-04-22 09:46:06.125[main][WARN][SystemUtil] - Get process CMD from CMDB error Connection refused
2025-04-22 09:46:06.125[main][INFO][SystemUtil] - Get AppProcessId:3c723a70-2f03-45eb-973f-a895f67e3aa4
2025-04-22 09:46:06.125[main][INFO][AuthServiceImpl] - Init refresh token task successful
2025-04-22 09:46:06.133[main][ERROR][AuthServiceImpl] - Try use IMDSv2 get token with error Host is down, will try to use IMSDv1
2025-04-22 09:46:06.135[main][ERROR][CSMSApplicationContextInitializer] - Get secrets from CSMS server failed
us.zoom.cloud.secrets.exception.CSMSCredException: Get EC2 STS failed: Host is down
	at us.zoom.cloud.secrets.api.impl.AuthServiceImpl.getCredKey(AuthServiceImpl.java:178)
	at us.zoom.cloud.secrets.api.impl.AuthServiceImpl.getCredToken(AuthServiceImpl.java:155)
	at us.zoom.cloud.secrets.api.impl.AuthServiceImpl.getToken(AuthServiceImpl.java:109)
	at us.zoom.cloud.secrets.api.impl.BaseRestApi.withRetryReq(BaseRestApi.java:142)
	at us.zoom.cloud.secrets.api.impl.BaseRestApi.withRetryReq(BaseRestApi.java:125)
	at us.zoom.cloud.secrets.api.impl.CSMSRestApiImpl.withRetryQuery(CSMSRestApiImpl.java:266)
	at us.zoom.cloud.secrets.api.impl.CSMSRestApiImpl.get(CSMSRestApiImpl.java:45)
	at us.zoom.cloud.secrets.service.impl.BaseCSMSService.getByPath(BaseCSMSService.java:82)
	at us.zoom.cloud.secrets.service.impl.CSMSServiceImpl.getAll(CSMSServiceImpl.java:32)
	at us.zoom.cloud.secrets.service.impl.CSMSServiceImpl.getAll(CSMSServiceImpl.java:23)
	at us.zoom.cloud.secrets.env.springboot.CSMSApplicationContextInitializer.initialize(CSMSApplicationContextInitializer.java:30)
	at org.springframework.boot.SpringApplication.applyInitializers(SpringApplication.java:612)
	at org.springframework.boot.SpringApplication.prepareContext(SpringApplication.java:383)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:317)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1361)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1350)
	at us.zoom.cschedule.schedule.CscheduleScheduleApplication.main(CscheduleScheduleApplication.java:21)
2025-04-22 09:46:06.135[refreshToken][ERROR][AuthServiceImpl] - Try use IMDSv2 get token with error Host is down, will try to use IMSDv1
2025-04-22 09:46:06.137[refreshToken][ERROR][AuthServiceImpl$RefreshTokenTask] - Get EC2 STS error Host is down
2025-04-22 09:46:06.139[refreshToken][ERROR][AuthServiceImpl] - Try use IMDSv2 get token with error Host is down, will try to use IMSDv1
2025-04-22 09:46:06.139[refreshToken][WARN][AuthServiceImpl$RefreshTokenTask] - Refresh JWT error Get EC2 STS failed: Host is down
2025-04-22 09:46:06.139[refreshToken][INFO][AuthServiceImpl$RefreshTokenTask] - Refresh JWT elapsed 2ms
2025-04-22 09:46:06.748[main][INFO][CSMSMultiServiceBridgeInitializer] - Init CSMSMultiServiceBridge successful
2025-04-22 09:46:06.749[main][WARN][CSMSMergeApplicationContextInitializer] - Ignore merge operate, because not find csmsPropertyResource
2025-04-22 09:46:06.750[main][INFO][CscheduleScheduleApplication] - Starting CscheduleScheduleApplication using Java 17.0.12 with PID 86491 (/Users/<USER>/IdeaGitProjects/cube-scheduler/cschedule-schedule/target/classes started by StarlsDing in /Users/<USER>/IdeaGitProjects/cube-scheduler)
2025-04-22 09:46:06.751[main][INFO][CscheduleScheduleApplication] - No active profile set, falling back to 1 default profile: "default"
2025-04-22 09:46:08.000[main][INFO][RotateHandlerStore] - Register secret keys [cube.dataSource.username, cube.dataSource.password] with HikariCPHotRotateHandler CurrentVersionHandler
2025-04-22 09:46:08.001[main][INFO][CSMSHotReloadMetricServiceImpl] - Start report hot reload metric task successful
2025-04-22 09:46:08.002[main][INFO][CSMSHotReloadMetricServiceImpl] - Add hot reload metrics secret cube.dataSource.username to map.
2025-04-22 09:46:08.002[main][INFO][CSMSHotReloadMetricServiceImpl] - Add hot reload metrics secret cube.dataSource.password to map.
2025-04-22 09:46:08.002[main][INFO][HikariDataSource] - [mysql:rotation] the datasource name[cube-scheduler-datasource] ,these keys [cube.dataSource.username,cube.dataSource.password] will be monitored and support hot reload
2025-04-22 09:46:08.004[main][INFO][RotateHandlerStore] - Register secret keys [cube.dataSource.username, cube.dataSource.password] with HikariCPHotRotateHandler CurrentVersionHandler
2025-04-22 09:46:08.005[main][INFO][HikariDataSource] - [mysql:rotation] the datasource name[cube-scheduler-datasource] ,these keys [cube.dataSource.username,cube.dataSource.password] will be monitored and support hot reload
2025-04-22 09:46:08.022[main][INFO][MultipleSchedulerConfig] - load quartz properties from classpath successfully, properties: {org.quartz.scheduler.batchTriggerAcquisitionMaxCount=100, org.quartz.scheduler.instanceId=AUTO, org.quartz.threadPool.threadCount=100, org.quartz.jobStore.acquireTriggersWithinLock=true, org.quartz.scheduler.instanceName=quartzScheduler, org.quartz.jobStore.lockHandler.class=org.quartz.impl.jdbcjobstore.SimpleSemaphore, org.quartz.jobStore.tablePrefix=QRTZ_, org.quartz.scheduler.batchTriggerAcquisitionFireAheadTimeWindow=10000}
2025-04-22 09:46:08.039[main][INFO][StdSchedulerFactory] - Using custom data access locking (synchronization): org.quartz.impl.jdbcjobstore.SimpleSemaphore
2025-04-22 09:46:08.039[main][INFO][StdSchedulerFactory] - Using default implementation for ThreadExecutor
2025-04-22 09:46:08.049[main][INFO][SchedulerSignalerImpl] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-04-22 09:46:08.049[main][INFO][QuartzScheduler] - Quartz Scheduler v.2.3.2 created.
2025-04-22 09:46:08.053[main][ERROR][HikariConfig] - cube-scheduler-datasource - dataSource or dataSourceClassName or jdbcUrl is required.
2025-04-22 09:46:08.053[main][INFO][QuartzScheduler] - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
2025-04-22 09:46:08.053[main][INFO][QuartzScheduler] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-04-22 09:46:08.053[main][DEBUG][SimpleThreadPool] - Shutting down threadpool...
2025-04-22 09:46:08.053[main][DEBUG][SimpleThreadPool] - Shutdown of threadpool complete.
2025-04-22 09:46:08.053[main][INFO][QuartzScheduler] - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
2025-04-22 09:46:08.547[quartzScheduler_Worker-3][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:46:08.548[quartzScheduler_Worker-2][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:46:08.548[quartzScheduler_Worker-7][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:46:08.548[quartzScheduler_Worker-11][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:46:08.549[quartzScheduler_Worker-15][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:46:08.549[quartzScheduler_Worker-20][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:46:08.549[quartzScheduler_Worker-18][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:46:08.549[quartzScheduler_Worker-21][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:46:08.549[quartzScheduler_Worker-23][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:46:08.550[quartzScheduler_Worker-22][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:46:08.550[quartzScheduler_Worker-28][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:46:08.550[quartzScheduler_Worker-33][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:46:08.550[quartzScheduler_Worker-24][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:46:08.550[quartzScheduler_Worker-31][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:46:08.550[quartzScheduler_Worker-42][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:46:08.550[quartzScheduler_Worker-32][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:46:08.550[quartzScheduler_Worker-30][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:46:08.550[quartzScheduler_Worker-5][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:46:08.550[quartzScheduler_Worker-8][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:46:08.550[quartzScheduler_Worker-48][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:46:08.550[quartzScheduler_Worker-36][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:46:08.551[quartzScheduler_Worker-10][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:46:08.550[quartzScheduler_Worker-46][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:46:08.550[quartzScheduler_Worker-39][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:46:08.551[quartzScheduler_Worker-12][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:46:08.551[quartzScheduler_Worker-53][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:46:08.551[quartzScheduler_Worker-13][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:46:08.551[quartzScheduler_Worker-57][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:46:08.551[quartzScheduler_Worker-55][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:46:08.550[quartzScheduler_Worker-4][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:46:08.551[quartzScheduler_Worker-60][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:46:08.551[quartzScheduler_Worker-43][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:46:08.551[quartzScheduler_Worker-79][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:46:08.551[quartzScheduler_Worker-16][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:46:08.551[quartzScheduler_Worker-98][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:46:08.551[quartzScheduler_Worker-82][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:46:08.551[quartzScheduler_Worker-86][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:46:08.551[quartzScheduler_Worker-78][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:46:08.551[quartzScheduler_Worker-80][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:46:08.550[quartzScheduler_Worker-37][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:46:08.552[quartzScheduler_Worker-85][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:46:08.552[quartzScheduler_Worker-9][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:46:08.552[quartzScheduler_Worker-26][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:46:08.552[quartzScheduler_Worker-97][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:46:08.551[quartzScheduler_Worker-70][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:46:08.552[quartzScheduler_Worker-54][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:46:08.552[quartzScheduler_Worker-45][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:46:08.552[quartzScheduler_Worker-19][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:46:08.552[quartzScheduler_Worker-34][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:46:08.552[quartzScheduler_Worker-50][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:46:08.552[quartzScheduler_Worker-96][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:46:08.551[quartzScheduler_Worker-40][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:46:08.552[quartzScheduler_Worker-64][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:46:08.552[quartzScheduler_Worker-88][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:46:08.552[quartzScheduler_Worker-29][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:46:08.551[quartzScheduler_Worker-49][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:46:08.552[quartzScheduler_Worker-25][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:46:08.551[quartzScheduler_Worker-41][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:46:08.552[quartzScheduler_Worker-62][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:46:08.550[quartzScheduler_Worker-6][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:46:08.552[quartzScheduler_Worker-52][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:46:08.552[quartzScheduler_Worker-38][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:46:08.551[quartzScheduler_Worker-47][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:46:08.552[quartzScheduler_Worker-76][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:46:08.551[quartzScheduler_Worker-14][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:46:08.553[quartzScheduler_Worker-51][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:46:08.552[quartzScheduler_Worker-81][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:46:08.553[quartzScheduler_Worker-100][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:46:08.553[quartzScheduler_Worker-99][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:46:08.551[quartzScheduler_Worker-68][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:46:08.552[quartzScheduler_Worker-84][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:46:08.551[quartzScheduler_Worker-56][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:46:08.551[quartzScheduler_Worker-75][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:46:08.552[quartzScheduler_Worker-90][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:46:08.551[quartzScheduler_Worker-61][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:46:08.552[quartzScheduler_Worker-27][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:46:08.551[quartzScheduler_Worker-74][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:46:08.553[quartzScheduler_Worker-71][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:46:08.551[quartzScheduler_Worker-59][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:46:08.551[quartzScheduler_Worker-73][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:46:08.551[quartzScheduler_Worker-83][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:46:08.551[quartzScheduler_Worker-72][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:46:08.551[quartzScheduler_Worker-77][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:46:08.551[quartzScheduler_Worker-92][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:46:08.552[quartzScheduler_Worker-91][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:46:08.552[quartzScheduler_Worker-95][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:46:08.551[quartzScheduler_Worker-69][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:46:08.552[quartzScheduler_Worker-93][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:46:08.552[quartzScheduler_Worker-17][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:46:08.552[quartzScheduler_Worker-35][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:46:08.552[quartzScheduler_Worker-67][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:46:08.552[quartzScheduler_Worker-87][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:46:08.552[quartzScheduler_Worker-63][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:46:08.552[quartzScheduler_Worker-44][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:46:08.552[quartzScheduler_Worker-58][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:46:08.552[quartzScheduler_Worker-65][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:46:08.553[quartzScheduler_Worker-66][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:46:08.552[quartzScheduler_Worker-94][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:46:08.552[quartzScheduler_Worker-89][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:46:08.547[quartzScheduler_Worker-1][DEBUG][SimpleThreadPool] - WorkerThread is shut down.
2025-04-22 09:46:13.876[CSMS thread shutdown][INFO][CSMSBeanFactory] - start execute CSMS shutdown
2025-04-22 09:57:32.735[main][INFO][CSMSUtil] - Find CSMS SDK config file in class path csms.properties
2025-04-22 09:57:32.737[main][INFO][CSMSBeanFactory] - add CSMS shutdown hook
2025-04-22 09:57:32.765[main][INFO][CSMSServiceProvider] - CSMS SDK config {"isEnable":true,"rotateConfig":{"enable":true,"pollingInterval":60,"maxThreads":20,"maxQueueSize":20,"maxWait":300},"metricConfig":{"enable":true,"reportInterval":1800,"hotReloadReportEnable":true,"hotReloadReportInterval":240},"endpoints":"https://csmsdev.zoomdev.us","path":"dev/cube","durationSeconds":3600,"sectionName":"default","httpOptions":{"connectTimeout":5000,"readTimeout":10000,"callTimeout":15000,"maxRetry":3,"maxIdleConnections":20,"keepAliveDuration":5,"retrySleepMillSecond":200},"isEnableIMDSv2":true,"maxRegisterPublicPathsCount":2000,"verifyPublicPath":true}
2025-04-22 09:57:32.778[main][INFO][CSMSMetricServiceImpl] - Start report metric task successful
2025-04-22 09:57:33.237[CSMS thread shutdown][INFO][CSMSBeanFactory] - start execute CSMS shutdown

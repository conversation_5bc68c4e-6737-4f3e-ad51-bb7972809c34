<?xml version="1.0" encoding="UTF-8"?>
<configuration>

    <property resource="application.properties"/>
    <property name="TRACE" value="%replace([%X{trace_id:-}:%X{span_id:-}]){'\[:\]', ''}"/>
    <property name="PATTERN" value="%d{yyyy-MM-dd HH:mm:ss.SSS}${TRACE}[%t][%p][%c{0}] - %m%n"/>
    <springProperty scope="context" name="LOG_PATH" source="log.filePath"/>

    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>%boldGreen(%date{ISO8601}) ${TRACE} %highlight([%-5level]) %boldCyan([%file]) %boldCyan([%line]) %yellow([%thread]) %boldMagenta(%logger{10}) %msg%n
            </pattern>
        </encoder>
    </appender>

    <appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_PATH:-cschedule-schedule/logs}/cschedule-schedule.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>
                ${LOG_PATH:-cschedule-schedule/logs}/%d{yyyy-MM-dd,aux}/cschedule-schedule.%d{yyyy-MM-dd_HH}.log.gz
            </fileNamePattern>
            <!-- keep 30 days'(720 hour) worth of history capped at 50GB total size -->
            <maxHistory>720</maxHistory>
            <totalSizeCap>${log.totalSizeCap:-5GB}</totalSizeCap>
        </rollingPolicy>
        <encoder>
            <pattern>${PATTERN}</pattern>
        </encoder>
    </appender>

    <appender name="ERROR_LOG" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_PATH:-logs}/cschedule-schedule-error.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${LOG_PATH:-logs}/%d{yyyy-MM-dd,aux}/cschedule-schedule-error.%d{yyyy-MM-dd_HH}.log.gz
            </fileNamePattern>
            <!-- keep 30 days'(720 hour) worth of history capped at 50GB total size -->
            <maxHistory>720</maxHistory>
            <totalSizeCap>${log.totalSizeCap:-5GB}</totalSizeCap>
        </rollingPolicy>
        <encoder>
            <pattern>${PATTERN}</pattern>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>ERROR</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
    </appender>

    <appender name="MONITOR_LOG" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_PATH:-logs}/cschedule-schedule-monitor.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${LOG_PATH:-logs}/%d{yyyy-MM-dd,aux}/cschedule-schedule-monitor.%d{yyyy-MM-dd_HH}.log.gz
            </fileNamePattern>
            <!-- keep 30 days'(720 hour) worth of history capped at 50GB total size -->
            <maxHistory>720</maxHistory>
            <totalSizeCap>${log.totalSizeCap:-5GB}</totalSizeCap>
        </rollingPolicy>
        <encoder>
            <pattern>%msg%n</pattern>
        </encoder>
    </appender>

    <appender name="ASYNC_MQ_LOG" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_PATH:-logs}/cschedule-schedule-async.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${LOG_PATH:-logs}/%d{yyyy-MM-dd,aux}/cschedule-schedule-async.%d{yyyy-MM-dd_HH}.log.gz</fileNamePattern>
            <!-- keep 30 days'(720 hour) worth of history capped at 5GB total size -->
            <maxHistory>720</maxHistory>
            <totalSizeCap>${log.totalSizeCap:-5GB}</totalSizeCap>
        </rollingPolicy>
        <encoder>
            <pattern>${PATTERN}</pattern>
        </encoder>
    </appender>

    <logger name="us.zoom.cschedule" level="info">
        <appender-ref ref="FILE"/>
        <appender-ref ref="ERROR_LOG"/>
    </logger>

    <logger name="us.zoom.cschedule.infra" level="info">
        <appender-ref ref="FILE"/>
        <appender-ref ref="ERROR_LOG"/>
    </logger>

    <logger name="us.zoom.mq" level="warn" additivity="false">
        <appender-ref ref="ASYNC_MQ_LOG"/>
        <appender-ref ref="ERROR_LOG"/>
        <appender-ref ref="STDOUT"/>
    </logger>

    <logger name="Monitor" level="TRACE" additivity="false">
        <appender-ref ref="MONITOR_LOG"/>
    </logger>

    <logger name="us.zoom.cloud" level="info" additivity="false">
        <appender-ref ref="STDOUT"/>
        <appender-ref ref="FILE"/>
    </logger>

    <logger name="com.zaxxer.hikari" level="info" additivity="false">
        <appender-ref ref="STDOUT"/>
        <appender-ref ref="FILE"/>
    </logger>

    <logger name="org.quartz.impl.jdbcjobstore.SimpleSemaphore" level="info" additivity="false">
        <appender-ref ref="STDOUT"/>
        <appender-ref ref="FILE"/>
    </logger>

    <logger name="org.quartz" level="debug" additivity="false">
        <appender-ref ref="STDOUT"/>
        <appender-ref ref="FILE"/>
    </logger>

    <root level="error">
        <appender-ref ref="STDOUT"/>
        <appender-ref ref="ERROR_LOG"/>
    </root>
</configuration>
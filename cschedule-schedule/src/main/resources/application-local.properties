# profile
spring.profiles.active=local

# MySQL data source
dataSource.url=**********************************************************************************************************************************************
#dataSource.url=***************************************************************************************************************************************************************************************************************
cube.dataSource.username=
cube.dataSource.password=

#Log Config
log.filePath=cschedule-schedule/logs

# async
async.mq.endpoint=
async.mq.username=
async.mq.password=
async.producer.pool.size=4

csms.jwt.public.key.path.list=dev/cube,ds01/cube
# server
server.port=8080
server.forward-headers-strategy=framework
server.tomcat.uri-encoding=UTF-8
# nws replaced tomcat with undertow
server.undertow.url-charset=UTF-8

# DruidDataSource setting
dataSource.filters=stat
dataSource.maxActive=10
dataSource.initialSize=1
dataSource.maxWait=60000
dataSource.minIdle=1
dataSource.timeBetweenEvictionRunsMillis=60000
dataSource.minEvictableIdleTimeMillis=300000
dataSource.testWhileIdle=true
dataSource.testOnBorrow=true
dataSource.testOnReturn=false
dataSource.poolPreparedStatements=true
dataSource.maxOpenPreparedStatements=20
dataSource.asyncInit=true
dataSource.ssl.enable=false

#Log Config
log.sampling.count.info=1000
log.sampling.count.warn=1000
log.sampling.count.error=10

## quartz
spring.quartz.auto-startup=false
spring.autoconfigure.exclude=org.springframework.boot.actuate.autoconfigure.quartz.QuartzEndpointAutoConfiguration

## only start one scheduler in single process
quartz.start.scheduler.instance.name=scheduler2

# auth config
auth.issuer.name=cube
auth.audience.name=hub
auth.secret.key=

# actuator
management.health.defaults.enabled=false
management.health.db.enabled=true
management.endpoint.health.show-components=always
management.endpoint.health.show-details=always
management.endpoints.web.exposure.include=info,prometheus,up,health

csms.jwt.public.key.path.list=dev/cube,ds01/cube

cube.config.client.endpoint=https://cubeconfig-perf-new.zoomdev.us
cube.config.dao.enable=false
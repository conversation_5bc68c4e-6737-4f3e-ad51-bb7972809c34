org.quartz.jobStore.tablePrefix=QRTZ_
org.quartz.scheduler.instanceName=quartzScheduler-3
org.quartz.scheduler.instanceId=AUTO
org.quartz.threadPool.threadCount=100
org.quartz.jobStore.lockHandler.class=org.quartz.impl.jdbcjobstore.SimpleSemaphore
org.quartz.jobStore.acquireTriggersWithinLock=true
org.quartz.scheduler.batchTriggerAcquisitionMaxCount=100
org.quartz.scheduler.batchTriggerAcquisitionFireAheadTimeWindow=10000
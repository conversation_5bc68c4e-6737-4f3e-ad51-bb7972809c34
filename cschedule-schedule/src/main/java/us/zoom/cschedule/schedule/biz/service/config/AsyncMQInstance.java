package us.zoom.cschedule.schedule.biz.service.config;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.RandomUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Service;
import us.zoom.cloud.secrets.rotate.handler.RotateHandlerRegister;
import us.zoom.cschedule.infra.utils.IpUtils;
import us.zoom.mq.client.AsyncMQ;
import us.zoom.mq.client.DefaultAsyncMQ;
import us.zoom.mq.client.clients.producer.Producer;
import us.zoom.mq.common.enums.ProtocolStrategy;

import java.util.ArrayList;
import java.util.List;

import static us.zoom.cschedule.schedule.core.config.AsyncMQInitializer.ASYNC_MQ_PASSWORD_KEY;

@Slf4j
@Service
public class AsyncMQInstance {

    private volatile static AsyncMQInstance instance;

    private volatile static List<Producer> producers = new ArrayList<>();
    private volatile static List<AsyncMQ> asyncMQS = new ArrayList<>();

    private static int producerPoolSize = 4;

    private AsyncMQInstance() {
    }

    public static void init(String endpoint, String username, String password, String producerPoolSizeStr) {
        if (instance == null) {
            synchronized (AsyncMQInstance.class) {
                if (instance == null) {
                    log.info("init AsyncMQInstance.");
                    if (StringUtils.isBlank(endpoint) || StringUtils.isBlank(username) || StringUtils.isBlank(password)) {
                        throw new RuntimeException("endpoint, username and password cannot be empty");
                    }
                    instance = new AsyncMQInstance();

                    try {
                        if (NumberUtils.isDigits(producerPoolSizeStr)) {
                            producerPoolSize = Integer.valueOf(producerPoolSizeStr);
                        }
                    } catch (Exception e) {
                        log.info("valueOf producerPoolSizeStr failed, value : " + producerPoolSizeStr);
                    }
                    log.info("async producer size : " + producerPoolSize);

                    for (int i = 0; i < producerPoolSize; i++) {
                        AsyncMQ asyncMQ = new DefaultAsyncMQ(endpoint, username, password);
                        asyncMQ.setClientId("async-client-" + IpUtils.getLocalIP() + "-" + i);
                        Producer producer = asyncMQ.producer();
                        producer.setProtocolStrategy(ProtocolStrategy.FULL);
                        producers.add(producer);
                        asyncMQS.add(asyncMQ);
                    }
                    log.info("init AsyncMQInstance success.");

                    // register asyncmq password rotate handler
                    registerRotateHandler(asyncMQS, username);
                    log.info("register asyncmq password rotate handler, username: " + username);
                }
            }
        }
    }

    public static AsyncMQInstance getInstance() {
        return instance;
    }

    public Producer getProducer() {
        return producers.get(RandomUtils.nextInt() % producerPoolSize);
    }

    public void shutdownProducer(String topic) {
        try {
            producers.forEach(producer -> {
                log.info("begin shutdownProducer, topic:" + topic);
                producer.shutdown(topic);
                log.info("shutdownProducer, topic:" + topic);
            });
        } catch (Exception e) {
            log.error("shutdownProducer failed, topic:" + topic, e.getMessage());
        }
    }

    public void shutdownAsyncMq() {
        asyncMQS.forEach(asyncMQ -> asyncMQ.shutdown());
    }

    private static void registerRotateHandler(List<AsyncMQ> asyncMQList, String username) {
        RotateHandlerRegister.registerCurrentVersionHandler(List.of(ASYNC_MQ_PASSWORD_KEY), new SchedulerCurrentVersionKVSecretHandler(asyncMQList, username));
    }
}

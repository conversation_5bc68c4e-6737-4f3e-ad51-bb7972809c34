package us.zoom.cschedule.schedule.biz.service.config;

import org.apache.commons.collections4.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import us.zoom.cloud.secrets.rotate.handler.CurrentVersionKVSecretHandler;
import us.zoom.cloud.secrets.rotate.vo.Status;
import us.zoom.cloud.secrets.vo.KVSecret;
import us.zoom.mq.client.AsyncMQ;
import us.zoom.mq.client.exception.AsyncMQClientException;

import java.util.List;
import java.util.Map;

import static us.zoom.cschedule.schedule.core.config.AsyncMQInitializer.ASYNC_MQ_PASSWORD_KEY;

/**
 * @author: eason.jia
 * @date: 2023/8/1
 */
public class SchedulerCurrentVersionKVSecretHandler implements CurrentVersionKVSecretHandler {

    private static final Logger logger = LoggerFactory.getLogger(SchedulerCurrentVersionKVSecretHandler.class);

    private List<AsyncMQ> asyncMQList;
    private String username;

    public SchedulerCurrentVersionKVSecretHandler(List<AsyncMQ> asyncMQList, String username) {
        this.asyncMQList = asyncMQList;
        this.username = username;
    }

    @Override
    public Map<String, Status> preVerify(Map<String, KVSecret> kvSecretMap) {
        if (MapUtils.isEmpty(kvSecretMap) || !kvSecretMap.containsKey(ASYNC_MQ_PASSWORD_KEY)) {
            logger.error("mq rotate secret map is empty or the map not contains {} key", ASYNC_MQ_PASSWORD_KEY);
            return Status.fail(kvSecretMap, "mq rotate secret map is empty or the map not contains specified key");
        }
        KVSecret secret = kvSecretMap.get(ASYNC_MQ_PASSWORD_KEY);
        try {
            for (AsyncMQ asyncMQ : asyncMQList) {
                if (!asyncMQ.verifyCredential(username, secret.getValue())) {
                    logger.error("mq preVerify failed, secret name = {}, version={}", secret.getSecretName(), secret.getVersionId());
                    return Status.fail(kvSecretMap, "mq preVerify failed");
                }
            }
            logger.info("mq preVerify success, secret name = {}, version={}", secret.getSecretName(), secret.getVersionId());
            return Status.success(kvSecretMap);
        } catch (AsyncMQClientException e) {
            logger.error("mq verify credential failed {}, secret name = {}, version={}", e.getMessage(), secret.getSecretName(), secret.getVersionId());
            return Status.fail(kvSecretMap, e.getMessage());
        }
    }

    @Override
    public Map<String, Status> handle(Map<String, KVSecret> kvSecretMap) {
        if (MapUtils.isEmpty(kvSecretMap) || !kvSecretMap.containsKey(ASYNC_MQ_PASSWORD_KEY)) {
            logger.error("mq rotate secret map is empty or the map not contains {} key", ASYNC_MQ_PASSWORD_KEY);
            return Status.fail(kvSecretMap, "mq rotate secret map is empty or the map not contains specified key");
        }
        KVSecret secret = kvSecretMap.get(ASYNC_MQ_PASSWORD_KEY);
        try {
            for (AsyncMQ asyncMQ : asyncMQList) {
                if (!asyncMQ.updatePassword(secret.getValue())) {
                    logger.error("mq handler failed, secret name = {}, version={}", secret.getSecretName(), secret.getVersionId());
                    return Status.fail(kvSecretMap, "mq handler failed");
                }
            }
            logger.info("mq handle success, secret name = {}, version={}", secret.getSecretName(), secret.getVersionId());
            return Status.success(kvSecretMap);
        } catch (AsyncMQClientException e) {
            logger.error("mq update password failed {}, secret name = {}, version={}", e.getMessage(), secret.getSecretName(), secret.getVersionId());
            return Status.fail(kvSecretMap, e.getMessage());
        }
    }
}
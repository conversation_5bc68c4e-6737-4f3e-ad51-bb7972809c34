package us.zoom.cschedule.schedule.core.quartz;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.quartz.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import us.zoom.cschedule.infra.enums.TimeTypeEnum;
import us.zoom.cschedule.infra.enums.TriggerTypeEnum;
import us.zoom.cschedule.infra.utils.JsonUtils;
import us.zoom.cschedule.schedule.core.model.job.JobInfo;
import us.zoom.cschedule.schedule.infra.CubeSchedulerException;
import us.zoom.cschedule.schedule.infra.ErrorCodeEnum;

import java.util.Date;
import java.util.List;
import java.util.Objects;

import static org.quartz.SimpleScheduleBuilder.simpleSchedule;


/**
 * <AUTHOR>
 * @date 2022-06-08 1:37
 */
@Component
@Slf4j
public class QuartzJobHandler {

    @Autowired
    private QuartzSchedulerRouteService quartzSchedulerRouteService;

    /**
     * add job
     */
    public void addJob(JobInfo jobInfo) {
        try {
            Objects.requireNonNull(jobInfo, "jobInfo is not null");
            Scheduler scheduler = quartzSchedulerRouteService.getScheduler(jobInfo);
            JobKey jobKey = JobKey.jobKey(jobInfo.getJobName(), jobInfo.getJobGroup());
            jobInfo.setClassName("us.zoom.cschedule.schedule.core.service.job.TaskEventJob");
            if (!scheduler.checkExists(jobKey)) {
                Class<Job> jobClass = (Class<Job>) Class.forName(jobInfo.getClassName());
                JobDetail jobDetail = JobBuilder
                        .newJob(jobClass)
                        .withIdentity(jobKey)
                        .storeDurably()
                        .withIdentity(jobInfo.getJobName(), jobInfo.getJobGroup())
                        .withDescription(jobInfo.getJobName())
                        .build();
                jobDetail.getJobDataMap().put("config", jobInfo.getConfig());
                jobDetail.getJobDataMap().put("jobInfo", JsonUtils.toJsonString(jobInfo));
                if (StringUtils.isEmpty(jobInfo.getTriggerName())) {
                    StringBuffer triggerName = new StringBuffer();
                    triggerName.append(jobInfo.getJobName()).append("_Trigger");
                    jobInfo.setTriggerName(triggerName.toString());
                }
                if (StringUtils.isEmpty(jobInfo.getTriggerGroup())) {
                    StringBuffer triggerGroup = new StringBuffer();
                    triggerGroup.append(jobInfo.getJobGroup()).append("_TriggerGroup");
                    jobInfo.setTriggerGroup(triggerGroup.toString());
                }
                if (null == jobInfo.getStartTime()) {
                    jobInfo.setStartTime(new Date());
                }

                TriggerKey triggerKey = TriggerKey.triggerKey(jobInfo.getTriggerName(), jobInfo.getTriggerGroup());
                Trigger trigger = creatTrigger(jobInfo, triggerKey);
                if (null == trigger) {
                    throw new CubeSchedulerException(ErrorCodeEnum.InnerError.getCode(), ErrorCodeEnum.InnerError.getErrMsg());
                }
                scheduler.scheduleJob(jobDetail, trigger);
            } else {
                throw new SchedulerException(jobInfo.getJobName() + "task is exist");
            }
        } catch (Exception e) {
            log.error("add job error!", e);
            throw new CubeSchedulerException(ErrorCodeEnum.InnerError.getCode(), ErrorCodeEnum.InnerError.getErrMsg());
        }
    }

    private Trigger creatTrigger(JobInfo jobInfo, TriggerKey triggerKey) {
        if (jobInfo.getTriggerType() == TriggerTypeEnum.CRON.getType()) {
            Trigger trigger = TriggerBuilder.newTrigger()
                    .withIdentity(triggerKey)
                    .startAt(jobInfo.getStartTime())
                    .endAt(jobInfo.getEndTime())
                    .withSchedule(CronScheduleBuilder.cronSchedule(jobInfo.getCron()))
                    .build();
            return trigger;
        } else if (jobInfo.getTriggerType() == TriggerTypeEnum.SIMPLE.getType()) {
            Trigger trigger = creatTriggerByTimeType(jobInfo, triggerKey);
            return trigger;
        }
        return null;
    }

    private Trigger creatTriggerByTimeType(JobInfo jobInfo, TriggerKey triggerKey) {
        if (null == jobInfo.getRepeatCount()) {
            jobInfo.setRepeatCount(-1);
        }
        if (jobInfo.getTimeType() == TimeTypeEnum.SECONDS.getType()) {
            Trigger trigger = TriggerBuilder.newTrigger()
                    .withIdentity(triggerKey)
                    .startAt(jobInfo.getStartTime())
                    .endAt(jobInfo.getEndTime())
                    .withSchedule(simpleSchedule()
                            .withIntervalInSeconds(jobInfo.getTimeNum())
                            .withRepeatCount(jobInfo.getRepeatCount()))
                    .build();
            return trigger;

        } else if (jobInfo.getTimeType() == TimeTypeEnum.MINUTE.getType()) {
            Trigger trigger = TriggerBuilder.newTrigger()
                    .withIdentity(triggerKey)
                    .startAt(jobInfo.getStartTime())
                    .endAt(jobInfo.getEndTime())
                    .withSchedule(simpleSchedule()
                            .withIntervalInMinutes(jobInfo.getTimeNum())
                            .withRepeatCount(jobInfo.getRepeatCount()))
                    .build();
            return trigger;
        } else if (jobInfo.getTimeType() == TimeTypeEnum.HOUR.getType()) {
            Trigger trigger = TriggerBuilder.newTrigger()
                    .withIdentity(triggerKey)
                    .startAt(jobInfo.getStartTime())
                    .endAt(jobInfo.getEndTime())
                    .withSchedule(simpleSchedule()
                            .withIntervalInHours(jobInfo.getTimeNum())
                            .withRepeatCount(jobInfo.getRepeatCount()))

                    .build();
            return trigger;
        } else if (jobInfo.getTimeType() == TimeTypeEnum.DAY.getType()) {
            Trigger trigger = TriggerBuilder.newTrigger()
                    .withIdentity(triggerKey)
                    .startAt(jobInfo.getStartTime())
                    .endAt(jobInfo.getEndTime())
                    .withSchedule(simpleSchedule()
                            .withIntervalInHours(24 * jobInfo.getTimeNum())
                            .withRepeatCount(jobInfo.getRepeatCount()))
                    .build();
            return trigger;
        }
        return null;
    }

    private SimpleTrigger changeTriggerByTimeType(JobInfo jobInfo, SimpleTrigger trigger, TriggerKey triggerKey) {
        if (null == jobInfo.getRepeatCount()) {
            jobInfo.setRepeatCount(-1);
        }
        if (jobInfo.getTimeType() == TimeTypeEnum.SECONDS.getType()) {
            trigger = trigger.getTriggerBuilder().withIdentity(triggerKey)
                    .withSchedule(simpleSchedule()
                            .withIntervalInSeconds(jobInfo.getTimeNum())
                            .withRepeatCount(jobInfo.getRepeatCount())).build();
            return trigger;

        } else if (jobInfo.getTimeType() == TimeTypeEnum.MINUTE.getType()) {
            trigger = trigger.getTriggerBuilder().withIdentity(triggerKey)
                    .withSchedule(simpleSchedule()
                            .withIntervalInMinutes(jobInfo.getTimeNum())
                            .withRepeatCount(jobInfo.getRepeatCount())).build();
            return trigger;
        } else if (jobInfo.getTimeType() == TimeTypeEnum.HOUR.getType()) {
            trigger = trigger.getTriggerBuilder().withIdentity(triggerKey)
                    .withSchedule(simpleSchedule()
                            .withIntervalInHours(jobInfo.getTimeNum())
                            .withRepeatCount(jobInfo.getRepeatCount())).build();
            return trigger;
        } else if (jobInfo.getTimeType() == TimeTypeEnum.DAY.getType()) {
            trigger = trigger.getTriggerBuilder().withIdentity(triggerKey)
                    .withSchedule(simpleSchedule()
                            .withIntervalInHours(24 * jobInfo.getTimeNum())
                            .withRepeatCount(jobInfo.getRepeatCount())).build();
            return trigger;
        }
        return null;
    }

    public void updateJobCron(JobInfo jobInfo) {
        try {
            Scheduler scheduler = quartzSchedulerRouteService.getScheduler(jobInfo);
            TriggerKey triggerKey = TriggerKey.triggerKey(jobInfo.getJobName(), jobInfo.getJobGroup());
            if (!scheduler.checkExists(triggerKey)) {
                scheduler = quartzSchedulerRouteService.getDefaultScheduler();
            }
            CronTrigger trigger = (CronTrigger) scheduler.getTrigger(triggerKey);
            log.info("new jobTime: {}", jobInfo.getCron());
            trigger = trigger.getTriggerBuilder().withIdentity(triggerKey)
                    .withSchedule(CronScheduleBuilder.cronSchedule(jobInfo.getCron())).build();
            scheduler.rescheduleJob(triggerKey, trigger);
        } catch (SchedulerException e) {
            log.error("update job error!", e);
            throw new CubeSchedulerException(ErrorCodeEnum.InnerError.getCode(), ErrorCodeEnum.InnerError.getErrMsg());
        }
    }

    public void updateJobByCron(JobInfo jobInfo) {
        try {
            Scheduler scheduler = quartzSchedulerRouteService.getScheduler(jobInfo);
            JobKey jobKey = JobKey.jobKey(jobInfo.getJobName(), jobInfo.getJobGroup());
            if (!scheduler.checkExists(jobKey)) {
                scheduler = quartzSchedulerRouteService.getDefaultScheduler();
            }
            JobDetail jobDetail = scheduler.getJobDetail(jobKey);
            jobDetail.getJobDataMap().put("config", jobInfo.getConfig());
            jobDetail.getJobDataMap().put("jobInfo", JsonUtils.toJsonString(jobInfo));
            TriggerKey triggerKey = TriggerKey.triggerKey(jobInfo.getJobName() + "_Trigger", jobInfo.getJobGroup() + "_TriggerGroup");
            CronTrigger trigger = (CronTrigger) scheduler.getTrigger(triggerKey);
            log.info("new jobTime: {}", jobInfo.getCron());
            CronScheduleBuilder scheduleBuilder = CronScheduleBuilder.cronSchedule(jobInfo.getCron());
            trigger = trigger.getTriggerBuilder().withIdentity(triggerKey)
                    .withSchedule(scheduleBuilder).build();
            scheduler.rescheduleJob(triggerKey, trigger);
            scheduler.addJob(jobDetail, true);
        } catch (SchedulerException e) {
            log.error("update job error!", e);
            throw new CubeSchedulerException(ErrorCodeEnum.InnerError.getCode(), ErrorCodeEnum.InnerError.getErrMsg());
        }
    }


    public void updateJobBySimple(JobInfo jobInfo) {
        try {
            Scheduler scheduler = quartzSchedulerRouteService.getScheduler(jobInfo);
            JobKey jobKey = JobKey.jobKey(jobInfo.getJobName(), jobInfo.getJobGroup());
            if (!scheduler.checkExists(jobKey)) {
                scheduler = quartzSchedulerRouteService.getDefaultScheduler();
            }
            JobDetail jobDetail = scheduler.getJobDetail(jobKey);
            jobDetail.getJobDataMap().put("config", jobInfo.getConfig());
            jobDetail.getJobDataMap().put("jobInfo", JsonUtils.toJsonString(jobInfo));
            TriggerKey triggerKey = TriggerKey.triggerKey(jobInfo.getJobName() + "_Trigger", jobInfo.getJobGroup() + "_TriggerGroup");
            SimpleTrigger trigger = (SimpleTrigger) scheduler.getTrigger(triggerKey);
            log.info("new jobTime: {} , {} ", jobInfo.getTimeNum(), jobInfo.getTimeType());
            trigger = changeTriggerByTimeType(jobInfo, trigger, triggerKey);
            scheduler.rescheduleJob(triggerKey, trigger);
            scheduler.addJob(jobDetail, true);
        } catch (SchedulerException e) {
            log.error("update job error!", e);
            throw new CubeSchedulerException(ErrorCodeEnum.InnerError.getCode(), ErrorCodeEnum.InnerError.getErrMsg());
        }
    }


    /**
     * pauseJob
     */
    public void pauseJob(JobInfo jobInfo) {
        try {
            Scheduler scheduler = quartzSchedulerRouteService.getScheduler(jobInfo);
            JobKey jobKey = JobKey.jobKey(jobInfo.getJobName(), jobInfo.getJobGroup());
            if (!scheduler.checkExists(jobKey)) {
                scheduler = quartzSchedulerRouteService.getDefaultScheduler();
            }
            JobDetail jobDetail = scheduler.getJobDetail(jobKey);
            if (jobDetail == null) {
                throw new CubeSchedulerException(ErrorCodeEnum.InnerError.getCode(), ErrorCodeEnum.InnerError.getErrMsg());
            }
            scheduler.pauseJob(jobKey);
        } catch (SchedulerException e) {
            log.error("pause job error!", e);
            throw new CubeSchedulerException(ErrorCodeEnum.InnerError.getCode(), ErrorCodeEnum.InnerError.getErrMsg());
        }
    }


    /**
     * resumeJob
     */
    public void resumeJob(JobInfo jobInfo) {
        try {
            Scheduler scheduler = quartzSchedulerRouteService.getScheduler(jobInfo);
            JobKey jobKey = JobKey.jobKey(jobInfo.getJobName(), jobInfo.getJobGroup());
            if (!scheduler.checkExists(jobKey)) {
                scheduler = quartzSchedulerRouteService.getDefaultScheduler();
            }
            JobDetail jobDetail = scheduler.getJobDetail(jobKey);
            if (null == jobDetail) {
                throw new CubeSchedulerException(ErrorCodeEnum.InnerError.getCode(), ErrorCodeEnum.InnerError.getErrMsg());
            }
            scheduler.resumeJob(jobKey);
        } catch (SchedulerException e) {
            log.error("resume job error!", e);
            throw new CubeSchedulerException(ErrorCodeEnum.InnerError.getCode(), ErrorCodeEnum.InnerError.getErrMsg());
        }
    }

    /**
     * deleteJob, if not found, return success
     */
    public void deleteJob(JobInfo jobInfo) {
        try {
            Scheduler scheduler = quartzSchedulerRouteService.getScheduler(jobInfo);
            JobKey jobKey = JobKey.jobKey(jobInfo.getJobName(), jobInfo.getJobGroup());
            if (!scheduler.checkExists(jobKey)) {
                scheduler = quartzSchedulerRouteService.getDefaultScheduler();
            }
            JobDetail jobDetail = scheduler.getJobDetail(jobKey);
            if (null == jobDetail) {
                log.warn("job don't exists, jobInfo: {}, schedule name: {}", jobInfo, scheduler.getSchedulerName());
                return;
            }
            scheduler.deleteJob(jobKey);
        } catch (Exception e) {
            log.error("delete job error!", e);
            throw new CubeSchedulerException(ErrorCodeEnum.InnerError.getCode(), ErrorCodeEnum.InnerError.getErrMsg());
        }
    }

    /**
     * getJobInfo
     */
    public JobInfo getJobInfo(String jobGroup, String jobName) {
        JobInfo jobInfo = new JobInfo();
        try {
            JobKey jobKey = JobKey.jobKey(jobName, jobGroup);
            Scheduler scheduler = quartzSchedulerRouteService.getScheduler(jobInfo);
            if (!scheduler.checkExists(jobKey)) {
                scheduler = quartzSchedulerRouteService.getDefaultScheduler();
            }
            if (!scheduler.checkExists(jobKey)) {
                return null;
            }
            List<? extends Trigger> triggers = scheduler.getTriggersOfJob(jobKey);
            if (Objects.isNull(triggers)) {
                throw new CubeSchedulerException(ErrorCodeEnum.InnerError.getCode(), ErrorCodeEnum.InnerError.getErrMsg());
            }
            TriggerKey triggerKey = triggers.get(0).getKey();
            Trigger.TriggerState triggerState = scheduler.getTriggerState(triggerKey);
            JobDetail jobDetail = scheduler.getJobDetail(jobKey);


            jobInfo.setJobName(jobName);
            jobInfo.setJobGroup(jobGroup);
            jobInfo.setTriggerName(triggerKey.getName());
            jobInfo.setTriggerGroup(triggerKey.getGroup());
            jobInfo.setClassName(jobDetail.getJobClass().getName());
            jobInfo.setStatus(triggerState.toString());

            if (Objects.nonNull(jobDetail.getJobDataMap())) {
                jobInfo.setConfig(JsonUtils.toJsonString(jobDetail.getJobDataMap()));
            }

            CronTrigger theTrigger = (CronTrigger) triggers.get(0);
            jobInfo.setCron(theTrigger.getCronExpression());
            return jobInfo;
        } catch (Exception e) {
            log.error("get job error!", e);
            throw new CubeSchedulerException(ErrorCodeEnum.InnerError.getCode(), ErrorCodeEnum.InnerError.getErrMsg());
        }
    }
}

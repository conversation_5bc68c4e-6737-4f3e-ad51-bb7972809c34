package us.zoom.cschedule.schedule.biz.service.config;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import us.zoom.manager.AbstractJWTSDKConfigMediator;

import java.util.List;

/**
 * @author: eason.jia
 * @date: 2023/5/24
 */
@Component
public class SchedulerJWTSDKConfigMediator extends AbstractJWTSDKConfigMediator {

    public static final String SCHEDULER_ASYMMETRIC_ISSUER = "scheduler";

    @Override
    public boolean enableVerifyFailedLog(String version, boolean current, String configKey, String issuer, String audience, int currentAlgIndex, int totalAlg) {
        return true;
    }

    @Override
    public boolean isLegalAudienceOfNewAsymmetricToken(List<String> audiences) {
        // here you need to check that if the audience of jwt token is your application
        for (String audience : audiences) {
            if (!StringUtils.equals(audience, SCHEDULER_ASYMMETRIC_ISSUER)) {
                return false;
            }
        }
        return true;
    }
}

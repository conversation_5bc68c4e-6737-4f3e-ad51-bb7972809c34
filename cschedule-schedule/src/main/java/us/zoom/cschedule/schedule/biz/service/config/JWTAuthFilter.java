package us.zoom.cschedule.schedule.biz.service.config;

import com.google.common.base.Splitter;
import jakarta.servlet.*;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import us.zoom.commons.jwt.service.JWTService;
import us.zoom.commons.jwt.service.TokenRegisterRequest;
import us.zoom.commons.jwt.service.TokenVerifiedRequest;
import us.zoom.commons.jwt.service.exception.TokenRegisterException;
import us.zoom.commons.jwt.service.impl.DefaultJWTService;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2022-06-20 10:46 
 */
public class JWTAuthFilter implements Filter {

    private static final Logger LOG = LoggerFactory.getLogger(JWTAuthFilter.class);
    private static final String AUTHENTICATION_BEARER = "Bearer ";
    public static final String CSMS_JWT_PUBLIC_KEY_PATH_LIST = "csms.jwt.public.key.path.list";
    private final JWTService jwtService = new DefaultJWTService();

    @Override
    public void init(FilterConfig filterConfig) throws ServletException {
        // asymmetric jwt token
        List<String> csmsPublicKeyPathList = getCsmsPublicKeyPathList(filterConfig);
        LOG.info("AuthFilter CSMS path list: " + csmsPublicKeyPathList);

        try {
            // asymmetric jwt token
            jwtService.register(TokenRegisterRequest.newOneOfAsymmetricVerifiedKey(csmsPublicKeyPathList, "ES256"));
        } catch (TokenRegisterException e) {
            LOG.error("token register failed", e);
            throw new IllegalStateException(e);
        }
    }

    private List<String> getCsmsPublicKeyPathList(FilterConfig filterConfig) {
        return Optional.ofNullable(filterConfig.getInitParameter(CSMS_JWT_PUBLIC_KEY_PATH_LIST))
                .map(s -> Splitter.on(",")
                        .omitEmptyStrings()
                        .trimResults()
                        .splitToList(s))
                .orElse(new ArrayList<>());
    }

    private void build401(HttpServletResponse response) throws IOException {
        response.setStatus(401);
        response.addHeader("X-Content-Type-Options", "nosniff");
        response.addHeader("Pragma", "no-cache, no-store, max-age=0, must-revalidate");
        response.addHeader("Expires", "nosniff");
        response.addHeader("Strict-Transport-Security", "max-age=31536000 ; includeSubDomains");
        response.addHeader("X-Frame-Options", "DENY");
        response.addHeader("X-XSS-Protection", "1; mode=block");
        response.getWriter().write("Forbidden");
        response.getWriter().flush();
    }

    @Override
    public void doFilter(ServletRequest servletRequest, ServletResponse servletResponse, FilterChain filterChain) throws IOException, ServletException {
        if (!(servletRequest instanceof HttpServletRequest)) {
            filterChain.doFilter(servletRequest, servletResponse);
            return;
        }

        HttpServletRequest request = (HttpServletRequest) servletRequest;
        HttpServletResponse response = (HttpServletResponse) servletResponse;
        String authenticationToken = request.getHeader("Authentication");
        if (StringUtils.isEmpty(authenticationToken) || authenticationToken.length() < AUTHENTICATION_BEARER.length()) {
            build401(response);
            return;
        }

        String token = authenticationToken.substring(AUTHENTICATION_BEARER.length(), authenticationToken.length());
        if (StringUtils.isEmpty(token)) {
            build401(response);
            return;
        }

        try {
            jwtService.verifyToken(TokenVerifiedRequest.newVerifyOne(token));
        } catch (Exception e) {
            build401(response);
            return;
        }

        //build session
        filterChain.doFilter(servletRequest, servletResponse);
    }
}

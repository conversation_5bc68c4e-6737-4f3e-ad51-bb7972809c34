package us.zoom.cschedule.schedule.infra;

/**
 * <AUTHOR>
 */
public enum ErrorCodeEnum {

    /**
     * 
     */
    PermitError("1101", "Permit error!"),

    /**
     * 
     */
    ParaError("1102", "Para error!"),

    /**
     * 
     */
    InnerError("500", "inner error"),

    /**
     * has no such alarm
     */
    HasNoSuchAlarm("70004", "You do not have such alarm!"),

    /**
     *
     */
    BadRequestParam("70011", "The request params are illegal!"),

    /**
     * EntityExisted
     */
    EntityExisted("20003", "the entity has already existed!"),

    /**
     * EntityNotExisted
     */
    EntityNotExisted("20004", "the entity is not existed!"),

    /**
     * EntityIsNull
     */
    EntityIsNull("20005", "the entity should not be null!"),

    ;

    private String code;

    private String errMsg;

    ErrorCodeEnum(String code) {
        this.code = code;
    }

    ErrorCodeEnum(String code, String errMsg) {
        this.code = code;
        this.errMsg = errMsg;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getErrMsg() {
        return errMsg;
    }

    public void setErrMsg(String errMsg) {
        this.errMsg = errMsg;
    }
}

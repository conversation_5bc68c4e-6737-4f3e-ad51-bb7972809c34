package us.zoom.cschedule.schedule.lib;


public class ResponseObject<T> {
    String status;
    String operId;
    String message;
    String operCode;
    T data;

    public static <T> ResponseObject<T> fail(String msg) {
        return fail(null, msg, null);
    }


    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getOperId() {
        return operId;
    }

    public void setOperId(String operId) {
        this.operId = operId;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public String getOperCode() {
        return operCode;
    }

    public void setOperCode(String operCode) {
        this.operCode = operCode;
    }

    public T getData() {
        return data;
    }

    public void setData(T data) {
        this.data = data;
    }

    public ResponseObject() {
    }

    public ResponseObject(String status, String operId, String message, T data) {
        this.status = status;
        this.operId = operId;
        this.message = message;
        this.data = data;
    }

    public ResponseObject(String status, String operId, String message, String operCode) {
        this.status = status;
        this.operId = operId;
        this.message = message;
        this.operCode = operCode;
    }

    public static <T> ResponseObject<T> success(String operId, T data) {
        return new ResponseObject<>(StatusEnum.SUCCESS.getStatus(), operId, null, data);
    }

    public static <T> ResponseObject<T> success(T data) {
        return new ResponseObject<>(StatusEnum.SUCCESS.getStatus(), null, null, data);
    }

    public static <T> ResponseObject<T> fail(String operId, String message, T data) {
        return new ResponseObject<>(StatusEnum.FAIL.getStatus(), operId, message, data);
    }

    public static <T> ResponseObject<T> failWithCode(String operId, String message, String code) {
        return new ResponseObject<>(StatusEnum.FAIL.getStatus(), operId, message, code);
    }
}

package us.zoom.cschedule.schedule.core.service.job;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.quartz.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import us.zoom.cschedule.infra.enums.JobTypeEnum;
import us.zoom.cschedule.infra.utils.JsonUtils;
import us.zoom.cschedule.schedule.core.model.job.JobInfo;
import us.zoom.cschedule.schedule.core.service.asyncMq.AsyncMqTaskService;
import us.zoom.cschedule.schedule.infra.LogSampling;
import us.zoom.cschedule.schedule.infra.MonitorLogUtil;
import us.zoom.cschedule.schedule.util.AsyncMqTaskUtils;
import us.zoom.cschedule.schedule.util.ThreadPoolExecutorUtils;

import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.atomic.AtomicLong;


/**
 * <AUTHOR>
 * @date 2022-03-09 3:28 
 */
@Slf4j
@Component
@DisallowConcurrentExecution
@PersistJobDataAfterExecution
public class TaskEventJob implements Job {

    private static final AtomicLong LOG_INDEX = new AtomicLong();

    @Override
    public void execute(JobExecutionContext context) throws JobExecutionException {
        JobInfo jobInfo = null;
        try {
            if (null != context.getJobDetail().getJobDataMap().get("jobInfo")) {
                jobInfo = JsonUtils.toObject((String) context.getJobDetail().getJobDataMap().get("jobInfo"), JobInfo.class);
                jobInfo.setClassName(null);
                if (StringUtils.equals(JobTypeEnum.ASYNCMQ.name(), jobInfo.getType())) {
                    AsyncMqTaskUtils.taskSend(jobInfo);
                    MonitorLogUtil.printSendMqMonitorLog(jobInfo, "sendMqSuccess");
                }
            }
        } catch (Exception e) {
            printErrorLog("send mq failed", e);
            if (jobInfo != null) {
                MonitorLogUtil.printSendMqMonitorLog(jobInfo, "sendMqFail");
            }
        }
    }

    private void printErrorLog(String message, Exception e) {
        if (LOG_INDEX.incrementAndGet() % LogSampling.getErrorSamplingCount() == 0) {
            log.error(message, e);
        }
        LOG_INDEX.compareAndSet(Long.MAX_VALUE, 0);
    }
}

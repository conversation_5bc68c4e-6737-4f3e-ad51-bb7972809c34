package us.zoom.cschedule.schedule.core.quartz;

import lombok.extern.slf4j.Slf4j;
import org.quartz.*;

/**
 * <AUTHOR>
 */
@Slf4j
public class CubeSchedulerListener implements SchedulerListener {
    @Override
    public void jobScheduled(Trigger trigger) {
        log.info("CubeSchedulerListener jobScheduled, key[{}] ", trigger.getKey());
    }

    @Override
    public void jobUnscheduled(TriggerKey triggerKey) {
        log.info("CubeSchedulerListener jobUnscheduled, key[{}] ", triggerKey);
    }

    @Override
    public void triggerFinalized(Trigger trigger) {
        log.info("CubeSchedulerListener triggerFinalized, key[{}] ", trigger.getKey());
    }

    @Override
    public void triggerPaused(TriggerKey triggerKey) {
        log.info("CubeSchedulerListener triggerPaused, key[{}] ", triggerKey);
    }

    @Override
    public void triggersPaused(String triggerGroup) {
        log.info("CubeSchedulerListener triggerPaused, key[{}] ", triggerGroup);
    }

    @Override
    public void triggerResumed(Trigger<PERSON>ey triggerKey) {
        log.info("CubeSchedulerListener triggerResumed, key[{}] ", triggerKey);
    }

    @Override
    public void triggersResumed(String triggerGroup) {
        log.info("CubeSchedulerListener triggersResumed, key[{}] ", triggerGroup);
    }

    @Override
    public void jobAdded(JobDetail jobDetail) {
        log.info("CubeSchedulerListener jobAdded, key[{}] ", jobDetail.getKey());
    }

    @Override
    public void jobDeleted(JobKey jobKey) {
        log.info("CubeSchedulerListener jobDeleted, key[{}] ", jobKey);
    }

    @Override
    public void jobPaused(JobKey jobKey) {
        log.info("CubeSchedulerListener jobPaused, key[{}] ", jobKey);
    }

    @Override
    public void jobsPaused(String jobGroup) {
        log.info("CubeSchedulerListener jobPaused, key[{}] ", jobGroup);
    }

    @Override
    public void jobResumed(JobKey jobKey) {
        log.info("CubeSchedulerListener jobResumed, key[{}] ", jobKey);
    }

    @Override
    public void jobsResumed(String jobGroup) {
        log.info("CubeSchedulerListener jobResumed, key[{}] ", jobGroup);
    }

    @Override
    public void schedulerError(String msg, SchedulerException cause) {
        log.info("CubeSchedulerListener schedulerError, msg[{}],", msg, cause);
    }

    @Override
    public void schedulerInStandbyMode() {
        log.info("CubeSchedulerListener schedulerInStandbyMode");
    }

    @Override
    public void schedulerStarted() {
        log.info("CubeSchedulerListener schedulerStarted");
    }

    @Override
    public void schedulerStarting() {
        log.info("CubeSchedulerListener schedulerStarting");
    }

    @Override
    public void schedulerShutdown() {
        log.info("CubeSchedulerListener schedulerShutdown");
    }

    @Override
    public void schedulerShuttingdown() {
        log.info("CubeSchedulerListener schedulerShuttingdown");
    }

    @Override
    public void schedulingDataCleared() {
        log.info("CubeSchedulerListener schedulingDataCleared");
    }
}

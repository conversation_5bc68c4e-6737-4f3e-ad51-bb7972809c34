package us.zoom.cschedule.schedule.lib;

import us.zoom.cschedule.schedule.infra.ErrorCodeEnum;

/**
 * <AUTHOR>
 */
public class AuthException extends RuntimeException {

    private String code;
    private String tips;

    public AuthException(ErrorCodeEnum codeEnum) {
        this.code = codeEnum.getCode();
        this.tips = codeEnum.getErrMsg();
    }

    public AuthException(String code, String tips) {
        this.code = code;
        this.tips = tips;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getTips() {
        return tips;
    }

    public void setTips(String tips) {
        this.tips = tips;
    }
}

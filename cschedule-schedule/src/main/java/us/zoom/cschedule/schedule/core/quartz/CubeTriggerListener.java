package us.zoom.cschedule.schedule.core.quartz;

import lombok.extern.slf4j.Slf4j;
import org.quartz.JobExecutionContext;
import org.quartz.Trigger;
import org.quartz.TriggerKey;
import org.quartz.TriggerListener;
import us.zoom.cschedule.schedule.infra.MonitorLogUtil;

@Slf4j
public class CubeTriggerListener implements TriggerListener {
    @Override
    public String getName() {
        return "CubeTriggerListener";
    }

    @Override
    public void triggerFired(Trigger trigger, JobExecutionContext context) {
        String jobName = context.getJobDetail().getKey().getName();
        String groupName = context.getJobDetail().getKey().getGroup();
        log.info("CubeTriggerListener triggerFired, jobName[{}], groupName[{}]", jobName, groupName);
    }

    @Override
    public boolean vetoJobExecution(Trigger trigger, JobExecutionContext context) {
        return false;
    }

    @Override
    public void triggerMisfired(Trigger trigger) {
        log.info("CubeTriggerListener triggerMisfired, key[{}], jobKey: [{}], PreviousFireTime: [{}], NextFireTime: [{}]", trigger.getKey(), trigger.getJobKey(), trigger.getPreviousFireTime(), trigger.getNextFireTime());
    }

    @Override
    public void triggerComplete(Trigger trigger, JobExecutionContext context, Trigger.CompletedExecutionInstruction triggerInstructionCode) {
        MonitorLogUtil.printCompleteMonitorLog(context, "triggerComplete",
                Trigger.CompletedExecutionInstruction.SET_TRIGGER_COMPLETE.equals(triggerInstructionCode)
                        || Trigger.CompletedExecutionInstruction.DELETE_TRIGGER.equals(triggerInstructionCode)
                        || Trigger.CompletedExecutionInstruction.SET_ALL_JOB_TRIGGERS_COMPLETE.equals(triggerInstructionCode)
                        || Trigger.CompletedExecutionInstruction.NOOP.equals(triggerInstructionCode));
    }
}

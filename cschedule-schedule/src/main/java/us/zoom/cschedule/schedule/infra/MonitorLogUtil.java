package us.zoom.cschedule.schedule.infra;

import org.quartz.JobExecutionContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import us.zoom.cschedule.infra.utils.IpUtils;
import us.zoom.cschedule.infra.utils.JsonUtils;
import us.zoom.cschedule.schedule.core.model.job.JobInfo;
import us.zoom.cube.sdk.model.MonitorLog;

import java.util.HashMap;
import java.util.Map;

public class MonitorLogUtil {
    private static final Logger monitorLogger = LoggerFactory.getLogger("Monitor");

    public static void printSendMqMonitorLog(JobInfo jobInfo, String measure) {

        Map<String, Object> tag = new HashMap<>(4);
        tag.put("topicName", jobInfo.getTopicName());
        tag.put("jobName", jobInfo.getJobName());
        tag.put("group", jobInfo.getJobGroup());
        tag.put("host", IpUtils.getHost());

        Map<String, Object> field = new HashMap<>(4);
        field.put("count", 1);

        MonitorLog monitorLog = MonitorLog.builder()
                .withMeasure(measure)
                .withCubeVer("1.0.0")
                .withTs(System.currentTimeMillis())
                .withNewTags(tag)
                .withNewFields(field)
                .build();

        monitorLogger.info(JsonUtils.toJsonStringIgnoreExp(monitorLog));
    }

    public static void printCompleteMonitorLog(JobExecutionContext context, String measure, boolean success) {
        if (null == context.getJobDetail().getJobDataMap().get("jobInfo")) {
            return;
        }
        JobInfo jobInfo = JsonUtils.toObject((String) context.getJobDetail().getJobDataMap().get("jobInfo"), JobInfo.class);
        Map<String, Object> tag = new HashMap<>(4);
        tag.put("topicName", jobInfo.getTopicName());
        tag.put("jobName", jobInfo.getJobName());
        tag.put("group", jobInfo.getJobGroup());
        tag.put("host", IpUtils.getHost());

        Map<String, Object> field = new HashMap<>(4);
        field.put("count", 1);
        field.put("isSuccess", success ? 1 : 0);
        field.put("runTime", context.getJobRunTime());

        MonitorLog monitorLog = MonitorLog.builder()
                .withMeasure(measure)
                .withCubeVer("1.0.0")
                .withTs(System.currentTimeMillis())
                .withNewTags(tag)
                .withNewFields(field)
                .build();

        monitorLogger.info(JsonUtils.toJsonStringIgnoreExp(monitorLog));
    }
}

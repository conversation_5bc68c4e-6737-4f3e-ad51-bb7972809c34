package us.zoom.cschedule.schedule.core.quartz;

import lombok.extern.slf4j.Slf4j;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.quartz.JobListener;

import static us.zoom.cschedule.schedule.infra.MonitorLogUtil.printCompleteMonitorLog;

@Slf4j
public class CubeJobListener implements JobListener {
    @Override
    public String getName() {
        return "CubeJobListener";
    }

    @Override
    public void jobToBeExecuted(JobExecutionContext context) {
        //log.info("CubeJobListener jobToBeExecuted, key[{}]", context.getJobDetail().getKey());
    }

    @Override
    public void jobExecutionVetoed(JobExecutionContext context) {
        log.info("CubeJobListener jobExecutionVetoed, key[{}]", context.getJobDetail().getKey());
    }

    @Override
    public void jobWasExecuted(JobExecutionContext context, JobExecutionException jobException) {
        if (null != jobException) {
            log.error("CubeJobListener jobWasExecuted, key[{}], ", context.getJobDetail().getKey(), jobException);
        }
        printCompleteMonitorLog(context, "jobComplete", null == jobException);
    }
}
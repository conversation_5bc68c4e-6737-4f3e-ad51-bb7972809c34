package us.zoom.cschedule.schedule.infra;


public class CubeSchedulerException extends RuntimeException {

    private String code;


    public CubeSchedulerException(String code, String msg) {
        super(msg);
        this.code = code;
    }

    public CubeSchedulerException(ErrorCodeEnum codeEnum) {

        super(codeEnum.getErrMsg());
        this.code = codeEnum.getCode();
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

}

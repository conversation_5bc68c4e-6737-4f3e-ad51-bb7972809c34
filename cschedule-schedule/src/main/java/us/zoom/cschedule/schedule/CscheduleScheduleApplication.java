package us.zoom.cschedule.schedule;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * <AUTHOR>
 * @Date 2020/10/15 2:34 
 */
@EnableScheduling
@SpringBootApplication
@ComponentScan(basePackages = {
        "us.zoom.cschedule.schedule",
        "us.zoom.cschedule.infra",
})
public class CscheduleScheduleApplication {

    public static void main(String[] args) {
        SpringApplication.run(CscheduleScheduleApplication.class, args);
    }

}

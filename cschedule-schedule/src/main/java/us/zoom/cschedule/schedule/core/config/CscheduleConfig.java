package us.zoom.cschedule.schedule.core.config;

import com.google.common.collect.Maps;
import jakarta.annotation.PostConstruct;
import org.quartz.SchedulerException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.Environment;
import org.springframework.scheduling.quartz.SchedulerFactoryBean;
import us.zoom.cschedule.schedule.biz.service.config.JWTAuthFilter;
import us.zoom.cschedule.schedule.core.quartz.CubeJobListener;
import us.zoom.cschedule.schedule.core.quartz.CubeSchedulerListener;
import us.zoom.cschedule.schedule.core.quartz.CubeTriggerListener;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022-06-20 2:43 
 */
@Configuration
public class CscheduleConfig {

    @Autowired
    private Environment environment;

    @Autowired
    private List<SchedulerFactoryBean> schedulerFactoryBeanList;

    @PostConstruct
    public void addListeners() throws SchedulerException {
        for (SchedulerFactoryBean schedulerFactoryBean : schedulerFactoryBeanList) {
            schedulerFactoryBean.getScheduler()
                    .getListenerManager()
                    .addSchedulerListener(new CubeSchedulerListener());
            schedulerFactoryBean.getScheduler().getListenerManager().addJobListener(new CubeJobListener());
            schedulerFactoryBean.getScheduler().getListenerManager().addTriggerListener(new CubeTriggerListener());
        }
    }

    @Bean
    public FilterRegistrationBean<JWTAuthFilter> ETEAuthFilter() {
        FilterRegistrationBean<JWTAuthFilter> authFilterFilterRegistrationBean = new FilterRegistrationBean<>();
        authFilterFilterRegistrationBean.setFilter(new JWTAuthFilter());
        authFilterFilterRegistrationBean.addUrlPatterns("/job/*");

        Map<String, String> initMap = Maps.newHashMap();
        initMap.put(JWTAuthFilter.CSMS_JWT_PUBLIC_KEY_PATH_LIST, environment.getProperty(JWTAuthFilter.CSMS_JWT_PUBLIC_KEY_PATH_LIST));
        authFilterFilterRegistrationBean.setInitParameters(initMap);

        authFilterFilterRegistrationBean.setName("ETEAuthFilter");
        authFilterFilterRegistrationBean.setOrder(1);
        return authFilterFilterRegistrationBean;
    }
}

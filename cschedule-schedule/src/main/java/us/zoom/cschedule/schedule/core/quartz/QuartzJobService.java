package us.zoom.cschedule.schedule.core.quartz;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import us.zoom.cschedule.infra.enums.JobTypeEnum;
import us.zoom.cschedule.infra.enums.TriggerTypeEnum;
import us.zoom.cschedule.infra.utils.JsonUtils;
import us.zoom.cschedule.schedule.core.model.job.JobInfo;
import us.zoom.cschedule.schedule.lib.ResponseObject;
import us.zoom.cube.lib.common.ProbeTaskStatusEnum;
import us.zoom.cube.lib.probe.SchedulerJobCfg;


/**
 * <AUTHOR>
 * @date 2022-06-08 1:43 
 */
@Component
@Slf4j
public class QuartzJobService {

    @Autowired
    private QuartzJobHandler quartzJobHandler;

    public ResponseObject addJob(JobInfo jobInfo) {
        Assert.notNull(jobInfo.getJobName(), "job name is null");
        Assert.notNull(jobInfo.getJobGroup(), "job group is null");
        Assert.notNull(jobInfo.getType(), "job type is null");
        Assert.notNull(jobInfo.getTriggerType(), "job trigger is null");
        if (TriggerTypeEnum.CRON.getType() == jobInfo.getTriggerType()) {
            Assert.notNull(jobInfo.getCron(), "job corn is null");
        }
        if (TriggerTypeEnum.SIMPLE.getType() == jobInfo.getTriggerType()) {
            Assert.notNull(jobInfo.getTimeType(), "job time type is null");
            Assert.notNull(jobInfo.getTimeNum(), "job time num is null");
        }
        if (JobTypeEnum.ASYNCMQ.name() == jobInfo.getType()) {
            Assert.notNull(jobInfo.getTopicName(), "job topic is null");
        }
        quartzJobHandler.addJob(jobInfo);
        return ResponseObject.success(jobInfo.getJobName());
    }

    public ResponseObject updateJobByCron(JobInfo jobInfo) {
        Assert.notNull(jobInfo.getJobName(), "job name is null");
        Assert.notNull(jobInfo.getJobGroup(), "job group is null");
        Assert.notNull(jobInfo.getCron(), "job corn is null");
        Assert.notNull(jobInfo.getConfig(), "job cfg is null");
        quartzJobHandler.updateJobByCron(jobInfo);
        return ResponseObject.success(jobInfo.getJobName());
    }

    public ResponseObject updateJobBySimple(JobInfo jobInfo) {
        Assert.notNull(jobInfo.getJobName(), "job name is null");
        Assert.notNull(jobInfo.getJobGroup(), "job group is null");
        Assert.notNull(jobInfo.getTriggerType(), "job trigger type is null");
        Assert.notNull(jobInfo.getTimeType(), "job time type is null");
        Assert.notNull(jobInfo.getTimeNum(), "job time num is null");
        Assert.notNull(jobInfo.getConfig(), "job cfg is null");
        quartzJobHandler.updateJobBySimple(jobInfo);

        return ResponseObject.success(jobInfo.getJobName());
    }

    public ResponseObject updateJobCron(JobInfo jobInfo) {
        Assert.notNull(jobInfo.getJobName(), "job name is null");
        Assert.notNull(jobInfo.getJobGroup(), "job group is null");
        Assert.notNull(jobInfo.getCron(), "job corn is null");
        quartzJobHandler.updateJobCron(jobInfo);
        return ResponseObject.success(jobInfo.getJobName());
    }

    public ResponseObject deleteJob(JobInfo jobInfo) {
        Assert.notNull(jobInfo.getJobName(), "job name is null");
        Assert.notNull(jobInfo.getJobGroup(), "job group is null");
        quartzJobHandler.deleteJob(jobInfo);
        return ResponseObject.success(jobInfo.getJobName());
    }

    public ResponseObject pauseJob(JobInfo jobInfo) {
        Assert.notNull(jobInfo.getJobName(), "job name is null");
        Assert.notNull(jobInfo.getJobGroup(), "job group is null");
        quartzJobHandler.pauseJob(jobInfo);
        return ResponseObject.success(jobInfo.getJobName());
    }

    public ResponseObject resumeJob(JobInfo jobInfo) {
        Assert.notNull(jobInfo.getJobName(), "job name is null");
        Assert.notNull(jobInfo.getJobGroup(), "job group is null");
        quartzJobHandler.resumeJob(jobInfo);
        return ResponseObject.success(jobInfo.getJobName());
    }

    public ResponseObject getJobInfo(JobInfo jobInfo) {
        Assert.notNull(jobInfo.getJobName(), "job name is null");
        Assert.notNull(jobInfo.getJobGroup(), "job group is null");
        JobInfo jobRes = quartzJobHandler.getJobInfo(jobInfo.getJobGroup(), jobInfo.getJobName());
        return ResponseObject.success(jobRes);
    }

    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public ResponseObject migrationJob(JobInfo jobInfo) {
        executeWithRetry(() -> deleteJob(jobInfo), "deleteJob");
        log.debug("delete job successfully when migrate job: {}", jobInfo.getJobName());
        executeWithRetry(() -> addJob(jobInfo), "addJob");
        log.debug("re-add job successfully when migrate job: {}", jobInfo.getJobName());

        try {
            SchedulerJobCfg schedulerJobCfg = JsonUtils.toObject(jobInfo.getConfig(), SchedulerJobCfg.class);
            if (StringUtils.equalsIgnoreCase(schedulerJobCfg.getStatus(), ProbeTaskStatusEnum.DISABLE.getDesc())) {
                pauseJob(jobInfo);
            }
        } catch (Exception e) {
            log.error("failed to pause job when migrate job: {}", jobInfo.getJobName(), e);
        }
        return ResponseObject.success(jobInfo.getJobName());
    }

    private static final int MAX_RETRY_TIMES = 3;

    private void executeWithRetry(Runnable runnable, String operationName) {
        int retryCount = 0;
        while (retryCount < MAX_RETRY_TIMES) {
            try {
                runnable.run();
                break;
            } catch (Exception e) {
                retryCount++;
                log.error("failed to {}, retryCount: {} ", operationName, retryCount, e);
                if (retryCount >= MAX_RETRY_TIMES) {
                    throw e;
                }
            }
        }
    }
}
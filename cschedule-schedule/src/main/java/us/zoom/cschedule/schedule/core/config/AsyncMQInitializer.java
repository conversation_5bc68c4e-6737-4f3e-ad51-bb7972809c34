package us.zoom.cschedule.schedule.core.config;

import jakarta.annotation.PostConstruct;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;
import us.zoom.cschedule.schedule.biz.service.config.AsyncMQInstance;


/**
 * <AUTHOR>
 * @Date 2020/10/15 2:34 
 */
@Component
public class AsyncMQInitializer {

    public static final String ASYNC_MQ_ENDPOINT_KEY = "async.mq.endpoint";
    public static final String ASYNC_MQ_USERNAME_KEY = "async.mq.username";
    public static final String ASYNC_MQ_PASSWORD_KEY = "async.mq.password";
    public static final String ASYNC_MQ_PRODUCER_POOL_SIZE_KEY = "async.producer.pool.size";

    @Autowired
    private Environment environment;

    @PostConstruct
    public void init() {
        AsyncMQInstance.init(
                environment.getProperty(ASYNC_MQ_ENDPOINT_KEY),
                environment.getProperty(ASYNC_MQ_USERNAME_KEY),
                environment.getProperty(ASYNC_MQ_PASSWORD_KEY),
                environment.getProperty(ASYNC_MQ_PRODUCER_POOL_SIZE_KEY)
        );
    }
}

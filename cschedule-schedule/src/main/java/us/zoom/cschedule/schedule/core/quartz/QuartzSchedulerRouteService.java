package us.zoom.cschedule.schedule.core.quartz;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Maps;
import org.apache.commons.lang3.StringUtils;
import org.quartz.Scheduler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import us.zoom.cschedule.infra.utils.JsonUtils;
import us.zoom.cschedule.schedule.core.model.job.JobInfo;
import us.zoom.cube.lib.probe.SchedulerJobCfg;

import java.util.Map;

/**
 * @author: eason.jia
 * @date: 2024/12/29
 */
@Service
public class QuartzSchedulerRouteService {

    private static final Logger logger = LoggerFactory.getLogger(QuartzSchedulerRouteService.class);
    private static final String E2E_JOB_GROUP_NAME = "cube_probe";
    private static final String DEFAULT_SCHEDULER_BEAN_NAME = "scheduler";
    private Map<String/**service name*/, String/**scheduler bean name*/> serviceSchedulerMapping = Maps.newHashMap();

    @Autowired
    private Map<String/**scheduler bean name*/, Scheduler> schedulerMap;

    public Scheduler getScheduler(JobInfo jobInfo) {
        if (jobInfo == null || !StringUtils.equals(E2E_JOB_GROUP_NAME, jobInfo.getJobGroup())) {
            return schedulerMap.get(DEFAULT_SCHEDULER_BEAN_NAME);
        }

        if (StringUtils.isBlank(jobInfo.getConfig())) {
            return schedulerMap.get(DEFAULT_SCHEDULER_BEAN_NAME);
        }

        try {
            SchedulerJobCfg schedulerJobCfg = JsonUtils.toObject(jobInfo.getConfig(), SchedulerJobCfg.class);
            String serviceName = schedulerJobCfg.getServiceName();
            String schedulerName = serviceSchedulerMapping.getOrDefault(serviceName, DEFAULT_SCHEDULER_BEAN_NAME);
            return schedulerMap.get(schedulerName);
        } catch (Exception e) {
            logger.error("failed to get scheduler, jobInfo: {}", jobInfo, e);
            return schedulerMap.get(DEFAULT_SCHEDULER_BEAN_NAME);
        }
    }

    public Scheduler getDefaultScheduler() {
        return schedulerMap.get(DEFAULT_SCHEDULER_BEAN_NAME);
    }

    public boolean hasServiceMapping(String serviceName) {
        return serviceSchedulerMapping.containsKey(serviceName);
    }

    public void refreshServiceSchedulerMapping(String sysPara) {
        try {
            Map<String, String> mapping = JsonUtils.toObjectByTypeRef(sysPara, new TypeReference<Map<String, String>>() {
            });
            serviceSchedulerMapping = mapping;
            logger.info("refresh service scheduler mapping, serviceSchedulerMapping: {}", serviceSchedulerMapping);
        } catch (Exception e) {
            logger.error("failed to deserialize system param: {}", sysPara, e);
        }
    }
}
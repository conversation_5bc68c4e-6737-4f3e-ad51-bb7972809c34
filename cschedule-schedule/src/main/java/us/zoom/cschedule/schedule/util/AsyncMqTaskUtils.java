package us.zoom.cschedule.schedule.util;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import us.zoom.cschedule.infra.enums.TaskTypeEnum;
import us.zoom.cschedule.infra.utils.JsonUtils;
import us.zoom.cschedule.schedule.biz.service.config.AsyncMQInstance;
import us.zoom.cschedule.schedule.core.model.job.JobInfo;
import us.zoom.cschedule.schedule.infra.LogSampling;
import us.zoom.mq.common.client.task.Task;

import java.nio.charset.StandardCharsets;
import java.util.concurrent.atomic.AtomicLong;

/**
 * <AUTHOR>
 * @date 2022-06-23 3:12 下午
 */
@Slf4j
public class AsyncMqTaskUtils {

    private static final AtomicLong LOG_INDEX = new AtomicLong();

    public static void taskSend(JobInfo jobInfo) {
        if (!StringUtils.isEmpty(jobInfo.getTopicName())) {
            Task<String> sourceTask = new Task<>();
            sourceTask.setTopicName(jobInfo.getTopicName());
            sourceTask.setTaskType(TaskTypeEnum.SCHEDULE_ETE.name());
            sourceTask.setPayload(JsonUtils.toJsonString(jobInfo));
            Task<byte[]> tmpTask = toByteTask(sourceTask);
            AsyncMQInstance.getInstance().getProducer().sendAsync(tmpTask);
        }
    }

    private static Task<byte[]> toByteTask(Task<String> sourceTask) {
        Task<byte[]> task = new Task();
        task.setPayload(sourceTask.getPayload().getBytes(StandardCharsets.UTF_8));
        task.setTopicName(sourceTask.getTopicName());
        task.setTaskType(sourceTask.getTaskType());
        task.setEcho(sourceTask.getEcho());
        task.setKey(sourceTask.getKey());
        task.setName(sourceTask.getName());
        task.setEncryptEnable(sourceTask.isEncryptEnable());
        task.setExtraInfo(sourceTask.getExtraInfo());
        task.setTrackingInfo(sourceTask.getTrackingInfo());
        task.setFilterParams(sourceTask.getFilterParams());
        return task;
    }

    private static void printErrorLog(String message, Exception e) {
        if (LOG_INDEX.incrementAndGet() % LogSampling.getErrorSamplingCount() == 0) {
            log.error(message, e);
        }
        LOG_INDEX.compareAndSet(Long.MAX_VALUE, 0);
    }
}

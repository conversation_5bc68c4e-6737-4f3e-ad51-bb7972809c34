package us.zoom.cschedule.schedule.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Calendar;
import java.util.Date;
import java.util.Optional;
import java.util.TimeZone;
import java.util.concurrent.TimeUnit;

/**
 * @author: eason.jia
 * @date: 2024/7/31
 */
public class DateUtils {

    private static final Logger log = LoggerFactory.getLogger(DateUtils.class.getName());

    private static TimeZone TIME_ZONE = TimeZone.getTimeZone("UTC");
    public static final String DATE_DEFAULT = "1970-01-01 01:00:00";
    public static final String FORMART1 = "yyyy-MM-dd HH:mm:ss";
    public static final String FORMART2 = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'";
    public static final String FORMART3 = "yyyy-MM-dd'T'HH:mm:ss'Z'";
    public static final String FORMART4 = "yyyy-MM-dd'T'HH:mm:ss.SSSSSSSSS'Z'";
    public static final String FORMART5 = "yyyy-MM-dd";
    public static final String FORMART6 = "yyyy.MM.dd";
    public static final String FORMART7 = "yyyy.MM";
    public static final String FORMART8 = "yyyyMMdd'T'HHmmssSSS'Z'";
    public static final String FORMART9 = "HH:mm:ss";
    public static final String FORMART10 = "yyyy-MM-dd'T'HH:mm:ss'+Z'";
    public static final String FORMART11 = "yyyy-MM-dd HH:mm:ss:SSS";
    public static final String FORMART12 = "yyyy-MM-dd'T'HH:mm:sss'Z'";
    public static final String FORMART13 = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'";

    public static Date getTodayStartTime() {
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.HOUR_OF_DAY, 00);
        calendar.set(Calendar.MINUTE, 00);
        calendar.set(Calendar.SECOND, 00);
        return calendar.getTime();
    }

    public static Date nanoToDate(long nanoTime) {
        return new Date(TimeUnit.NANOSECONDS.toMillis(nanoTime));
    }

    public static String getCurrentDate() {
        DateTimeFormatter formatter1 = DateTimeFormatter.ofPattern(FORMART5);
        LocalDateTime now = LocalDateTime.now();
        return formatter1.format(now);
    }

    public static Date getSpcCurrentDate() {
        ZoneId zoneId = ZoneId.systemDefault();
        LocalDateTime localDate = LocalDateTime.now();
        ZonedDateTime zdt = localDate.atZone(zoneId);
        Date date = Date.from(zdt.toInstant());
        return date;
    }

    public static long getPeriodBeginTime(long inputTime, int period) {
        if (period == 0) {
            return inputTime;
        }
        return (inputTime / (period * 1000)) * (period * 1000);
    }

    public static String getCurrentDate(String format) {
        DateTimeFormatter formatter1 = DateTimeFormatter.ofPattern(format);
        LocalDateTime now = LocalDateTime.now();
        return formatter1.format(now);
    }

    public static String changeUTC2Date(long utcTime) {
        SimpleDateFormat formatter2 = new SimpleDateFormat(FORMART1);
        formatter2.setTimeZone(TIME_ZONE);
        return formatter2.format(new Date(utcTime));
    }

    public static Long addMinute(long time, int count) {
        return time + count * 60 * 1000;
    }

    public static Long addMinute(Date time, int count) {
        return time.getTime() + count * 60 * 1000;
    }


    public static Long addHour(Date time, int count) {
        return time.getTime() + count * 60 * 60 * 1000;
    }

    public static Long addDay(Date time, int count) {
        return time.getTime() + count * 24 * 60 * 60 * 1000L;
    }

    public static Long addDay(long time, int count) {
        return time + count * 24 * 60 * 60 * 1000L;
    }

    public static Long addSeconds(Date time, int count) {
        return time.getTime() + count * 1000;
    }

    public static long getCurrentUTCTime() {
        return getCurrentUTCDate().getTime();
    }

    public static Date getCurrentUTCDate() {
        String d = changeUTC2Date(System.currentTimeMillis());
        SimpleDateFormat sdf = new SimpleDateFormat(FORMART1);
        try {
            return sdf.parse(d);
        } catch (Exception e) {
            return null;
        }
    }

    public static String getDateWithTandZ() {
        SimpleDateFormat formatter4 = new SimpleDateFormat(FORMART8);
        formatter4.setTimeZone(TIME_ZONE);
        return formatter4.format(new Date());
    }

    public static String timeStamp2ISODate(long timeStamp) {
        SimpleDateFormat formatter5 = new SimpleDateFormat(FORMART2);
        Date date = new Date(timeStamp);
        formatter5.setTimeZone(TIME_ZONE);
        return formatter5.format(date);
    }


    public static Date parseDate(String date, String format) throws ParseException {
        SimpleDateFormat dateFormat = new SimpleDateFormat(format);
        return dateFormat.parse(date);
    }

    public static long changeDate2UTC(String dateStr, String format) {

        try {
            SimpleDateFormat dateFormat = new SimpleDateFormat(format);
            dateFormat.setTimeZone(TIME_ZONE);
            return dateFormat.parse(dateStr).getTime();
        } catch (ParseException e) {
            log.warn("{} change to utc time error:{}", dateStr, e);
        }
        return System.currentTimeMillis();
    }


    public static String format(Date date, String format) {
        SimpleDateFormat dateFormat = new SimpleDateFormat(format);
        return dateFormat.format(date);
    }

    public static String format(Long timestamp, String format) {
        SimpleDateFormat dateFormat = new SimpleDateFormat(format);
        return dateFormat.format(new Date(timestamp));
    }

    public static String format(Long timestamp, String format, String defaultValue) {
        return Optional.ofNullable(timestamp)
                .map(t -> format(timestamp, format))
                .orElse(defaultValue);
    }

    public static long nextMonitorTime(long date, int n) {
        return date - date % (1000 * 60 * n) + (1000 * 60 * n);
    }
}

package us.zoom.cschedule.schedule.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import us.zoom.cschedule.infra.utils.JsonUtils;
import us.zoom.cube.sdk.model.MonitorLog;

/**
 * @author: eason.jia
 * @date: 2024/8/5
 */
public class MonitorLogUtils {

    private static final Logger logger = LoggerFactory.getLogger("Monitor");

    public static void print(MonitorLog monitorLog) {
        logger.info(JsonUtils.toJsonStringIgnoreExp(monitorLog));
    }
}
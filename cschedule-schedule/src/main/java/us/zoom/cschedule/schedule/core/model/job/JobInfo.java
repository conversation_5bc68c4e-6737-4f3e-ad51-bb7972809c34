package us.zoom.cschedule.schedule.core.model.job;

import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2022-03-09 3:22 
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class JobInfo {
    /**
     * jobName
     */
    private String jobName;
    /**
     * jobGroup
     */
    private String jobGroup;
    /**
     * triggerName
     */
    private String triggerName;
    /**
     * triggerGroup
     */
    private String triggerGroup;
    /**
     * cron
     */
    private String cron;

    private Date startTime;

    private Date endTime;

    /**
     * ASYNCMQ(1),
     * KAFKA(2),
     * OTHER(3);
     */
    private String type;

    /**
     * ete(1)
     * other(2)
     */
    private Integer taskType;
    /**
     * 
     */
    private String topicName;

    /**
     * 
     */
    private String className;

    /**
     * status
     */
    private String status;
    /**
     * nextTime
     */
    private String nextTime;
    /**
     * prevTime
     */
    private String prevTime;

    /**
     * cron(1)
     * simple(2)
     */
    private Integer triggerType;

    /**
     * seconds(1)
     * minute(2)
     * hour(3)
     * day(4)
     */
    private Integer timeType;


    /**
     * time num
     */
    private Integer timeNum;

    /**
     * -1 repeat_ever
     * n repeat_count
     */
    private Integer repeatCount;

    /**
     * config
     */
    private String config;

    public String getJobName() {
        return jobName;
    }

    public void setJobName(String jobName) {
        this.jobName = jobName;
    }

    public String getJobGroup() {
        return jobGroup;
    }

    public void setJobGroup(String jobGroup) {
        this.jobGroup = jobGroup;
    }

    public String getTriggerName() {
        return triggerName;
    }

    public void setTriggerName(String triggerName) {
        this.triggerName = triggerName;
    }

    public String getTriggerGroup() {
        return triggerGroup;
    }

    public void setTriggerGroup(String triggerGroup) {
        this.triggerGroup = triggerGroup;
    }

    public String getCron() {
        return cron;
    }

    public void setCron(String cron) {
        this.cron = cron;
    }

    public String getClassName() {
        return className;
    }

    public void setClassName(String className) {
        this.className = className;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getNextTime() {
        return nextTime;
    }

    public void setNextTime(String nextTime) {
        this.nextTime = nextTime;
    }

    public String getPrevTime() {
        return prevTime;
    }

    public void setPrevTime(String prevTime) {
        this.prevTime = prevTime;
    }

    public String getConfig() {
        return config;
    }

    public void setConfig(String config) {
        this.config = config;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public Integer getTaskType() {
        return taskType;
    }

    public void setTaskType(Integer taskType) {
        this.taskType = taskType;
    }

    public Integer getTriggerType() {
        return triggerType;
    }

    public void setTriggerType(Integer triggerType) {
        this.triggerType = triggerType;
    }

    public Integer getTimeType() {
        return timeType;
    }

    public void setTimeType(Integer timeType) {
        this.timeType = timeType;
    }

    public Integer getTimeNum() {
        return timeNum;
    }

    public void setTimeNum(Integer timeNum) {
        this.timeNum = timeNum;
    }

    public String getTopicName() {
        return topicName;
    }

    public void setTopicName(String topicName) {
        this.topicName = topicName;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Integer getRepeatCount() {
        return repeatCount;
    }

    public void setRepeatCount(Integer repeatCount) {
        this.repeatCount = repeatCount;
    }
}

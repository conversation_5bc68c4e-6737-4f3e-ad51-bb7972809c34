package us.zoom.cschedule.schedule.infra;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;

/**
 * @<PERSON> <PERSON>
 * @create 2020/12/3 3:45 PM
 */
@Service
@Slf4j
public class LogSampling {

    private static ApplicationContext applicationContext;
    private static final long DEFAULT_SAMPLE_COUNT = 10000L;
    private static Long infoSamplingCount;
    private static Long warnSamplingCount;
    private static Long errorSamplingCount;

    public static long getInfoSamplingCount() {
        if (infoSamplingCount != null) {
            return infoSamplingCount;
        }
        String property = applicationContext.getEnvironment().getProperty("log.sampling.count.info");
        infoSamplingCount = property != null ? Long.parseLong(property) : DEFAULT_SAMPLE_COUNT;
        log.info("the infoSamplingCount is {}", infoSamplingCount);
        return infoSamplingCount;
    }

    public static long getWarnSamplingCount() {
        if (warnSamplingCount != null) {
            return warnSamplingCount;
        }
        String property = applicationContext.getEnvironment().getProperty("log.sampling.count.warn");
        warnSamplingCount = property != null ? Long.parseLong(property) : DEFAULT_SAMPLE_COUNT;
        log.info("the warnSamplingCount is {}", warnSamplingCount);
        return warnSamplingCount;
    }

    public static long getErrorSamplingCount() {
        if (errorSamplingCount != null) {
            return errorSamplingCount;
        }
        String property = applicationContext.getEnvironment().getProperty("log.sampling.count.error");
        errorSamplingCount = property != null ? Long.parseLong(property) : DEFAULT_SAMPLE_COUNT;
        log.info("the errorSamplingCount is {}", errorSamplingCount);
        return errorSamplingCount;
    }

    @Autowired
    public void initialize(ApplicationContext ac) {
        applicationContext = ac;
    }

}

package us.zoom.cschedule.infra.enums;

/**
 * <AUTHOR>
 * @date 2022-06-23 3:33 
 */
public enum TaskTypeEnum {

    SCHEDULE_ETE(1),

    OTHER(2);

    private int value;

    public int getValue() {
        return value;
    }

    TaskTypeEnum(int value) {
        this.value = value;
    }

    public static TaskTypeEnum fromValue(int value) {
        for (TaskTypeEnum type : TaskTypeEnum.values()) {
            if (type.value == value) {
                return type;
            }
        }
        return null;
    }
}

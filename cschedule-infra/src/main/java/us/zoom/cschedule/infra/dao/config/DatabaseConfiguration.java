package us.zoom.cschedule.infra.dao.config;

import com.zaxxer.hikari.HikariDataSource;
import org.springframework.context.EnvironmentAware;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.Environment;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.annotation.EnableTransactionManagement;
import org.springframework.transaction.support.DefaultTransactionDefinition;

import javax.sql.DataSource;

/**
 * <AUTHOR>
 * @create 2019/11/20 3:20 
 */
@Configuration
@EnableTransactionManagement
public class DatabaseConfiguration implements EnvironmentAware {

    private Environment environment;

    @Override
    public void setEnvironment(Environment environment) {
        this.environment = environment;
    }

    @Bean
    public DataSource dataSource() {
        preCheckSsl();
        HikariDataSource dataSource = new HikariDataSource();
        dataSource.setJdbcUrl(environment.getProperty("datasource.url"));
        dataSource.setUsername(environment.getProperty("cube.dataSource.username"));
        dataSource.setPassword(environment.getProperty("cube.dataSource.password"));
        dataSource.setMaximumPoolSize(environment.getProperty("dataSource.maxActive", Integer.class));
        dataSource.setMinimumIdle(environment.getProperty("dataSource.minIdle", Integer.class));
        dataSource.setCsmsRotateUsernameKey("cube.dataSource.username");
        dataSource.setCsmsRotatePasswordKey("cube.dataSource.password");
        dataSource.setPoolName("cube-scheduler-datasource");
        dataSource.registerCsmsHotRotate();
        return dataSource;
    }

    private void preCheckSsl() {
        Boolean sslEnabled = environment.getProperty("dataSource.ssl.enable", Boolean.class);
        if (Boolean.TRUE.equals(sslEnabled)) {
            if (System.getProperty("javax.net.ssl.trustStore") == null) {
                System.setProperty("javax.net.ssl.trustStore", environment.getProperty("dataSource.ssl.trustStore"));
            }
            if (System.getProperty("javax.net.ssl.trustStorePassword") == null) {
                System.setProperty("javax.net.ssl.trustStorePassword", environment.getProperty("dataSource.ssl.trustStorePassword"));
            }
        }
    }

    @Bean
    public DataSourceTransactionManager dataSourceTransactionManager() throws Exception {
        return new DataSourceTransactionManager(dataSource());
    }

    @Bean
    public TransactionDefinition transactionDefinition() throws Exception {
        DefaultTransactionDefinition definition = new DefaultTransactionDefinition();
        definition.setPropagationBehavior(TransactionDefinition.PROPAGATION_REQUIRES_NEW);
        return definition;
    }
}

package us.zoom.cschedule.infra.model.log;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MetricsLog {
    private String measure;
    private long ts;
    private Map<String, Object> tag;
    private Map<String, Object> field;
    private String cubeVer;
    private List<String> pii;

}
package us.zoom.cschedule.infra.model;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import us.zoom.cschedule.infra.model.log.MetricsLog;
import us.zoom.cschedule.infra.utils.JsonUtils;

/**
 * @Author: <PERSON>
 * @ModuleOwner: <PERSON>
 * @Date:04/14/2023 08:51
 * @Description:
 */
public class MetricsLogTest {
    @Test
    public void testMetricsLogDecode() {
        String sample = "{" +
                "    \"measure\":\"request\"," +
                "    \"tag\":{" +
                "        \"url\":\"/login\"," +
                "        \"status\":200," +
                "        \"accountId\": \"<E>s!r94B5csB4L1234</E>\"" +
                "    },\n" +
                "    \"ts\":*************," +
                "    \"cubeVer\": \"1.0.0\"," +
                "    \"field\":{" +
                "        \"request_time\": 10.0," +
                "        \"query_rds_count\": 90.0" +
                "    }," +
                "    \"pii\":[" +
                "        \"accountId\"" +
                "    ]" +
                "}";

        MetricsLog metricsLog = JsonUtils.toObject(sample, MetricsLog.class);
        Assertions.assertTrue(metricsLog != null);
        Assertions.assertTrue(metricsLog.getTs() > 0);
        Assertions.assertTrue(metricsLog.getCubeVer() != null);
        Assertions.assertTrue(metricsLog.getTag().size() == 3);
        Assertions.assertTrue(metricsLog.getField().size() == 2);
        Assertions.assertTrue(metricsLog.getPii().size() == 1);
    }
}

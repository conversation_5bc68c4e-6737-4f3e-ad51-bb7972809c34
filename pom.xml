<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>us.zoom</groupId>
    <artifactId>cschedule</artifactId>
    <packaging>pom</packaging>
    <version>0.0.1</version>

    <modules>
        <module>cschedule-infra</module>
        <module>cschedule-schedule</module>
        <module>cube-scheduler-lib</module>
        <module>cube-scheduler-admin</module>
        <module>cube-executor-pull-query</module>
    </modules>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <compiler.plugin.version>3.10.1</compiler.plugin.version>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
        <!-- dependency version -->
        <spring-boot.version>3.4.5</spring-boot.version>
        <nws-platform.version>3.2.240730</nws-platform.version>
        <asyncmq.version>2.7.13</asyncmq.version>
        <asyncmq.openapi.version>0.1.12</asyncmq.openapi.version>
        <csms.sdk.version>0.4.10</csms.sdk.version>
        <csms.version>0.6.16</csms.version>
        <cube-infra.version>2.0.15-RELEASE</cube-infra.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>us.zoom</groupId>
                <artifactId>cube-infra</artifactId>
                <version>${cube-infra.version}</version>
            </dependency>
            <!-- spring -->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring-boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- zoom -->
            <dependency>
                <groupId>us.zoom.commons</groupId>
                <artifactId>nws-platform-dependencies</artifactId>
                <version>${nws-platform.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>com.zaxxer</groupId>
                <artifactId>HikariCP</artifactId>
                <version>5.0.1.11-zoom</version>
            </dependency>

            <!-- utils -->
            <dependency>
                <groupId>commons-collections</groupId>
                <artifactId>commons-collections</artifactId>
                <version>3.2.2</version>
            </dependency>
            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>guava</artifactId>
                <version>33.3.0-jre</version>
            </dependency>
            <dependency>
                <groupId>com.squareup.okhttp3</groupId>
                <artifactId>okhttp</artifactId>
                <version>4.12.0</version>
            </dependency>

            <!-- fix security issues -->
            <dependency>
                <groupId>org.bouncycastle</groupId>
                <artifactId>bcprov-jdk18on</artifactId>
                <version>1.78.1</version>
            </dependency>
            <dependency>
                <groupId>org.bouncycastle</groupId>
                <artifactId>bcutil-jdk18on</artifactId>
                <version>1.78.1</version>
            </dependency>
            <dependency>
                <groupId>org.bouncycastle</groupId>
                <artifactId>bcpkix-jdk18on</artifactId>
                <version>1.78.1</version>
            </dependency>
            <dependency>
                <groupId>org.jboss.xnio</groupId>
                <artifactId>xnio-api</artifactId>
                <version>3.8.14.Final</version>
            </dependency>
            <dependency>
                <groupId>org.jboss.xnio</groupId>
                <artifactId>xnio-nio</artifactId>
                <version>3.8.14.Final</version>
            </dependency>
            <dependency>
                <groupId>io.undertow</groupId>
                <artifactId>undertow-core</artifactId>
                <version>2.3.16.Final</version>
            </dependency>
            <dependency>
                <groupId>io.undertow</groupId>
                <artifactId>undertow-servlet</artifactId>
                <version>2.3.16.Final</version>
            </dependency>
            <dependency>
                <groupId>io.undertow</groupId>
                <artifactId>undertow-websockets-jsr</artifactId>
                <version>2.3.16.Final</version>
            </dependency>
            <!-- asyncMQ Start -->
            <dependency>
                <groupId>us.zoom.mq</groupId>
                <artifactId>asyncmq-client-ha</artifactId>
                <version>${asyncmq.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.bouncycastle</groupId>
                        <artifactId>bcprov-jdk15on</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.bouncycastle</groupId>
                        <artifactId>bcpkix-jdk15on</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>us.zoom.async.mq</groupId>
                <artifactId>openapi-client</artifactId>
                <version>${asyncmq.openapi.version}</version>
            </dependency>
            <!-- asyncMQ End -->
            <!--csms dependencies-->
            <dependency>
                <groupId>us.zoom</groupId>
                <artifactId>cloud-secrets-management-service-sdk</artifactId>
                <version>${csms.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.squareup.okhttp3</groupId>
                        <artifactId>okhttp</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.google.code.gson</groupId>
                        <artifactId>gson</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.bouncycastle</groupId>
                        <artifactId>bcprov-jdk15on</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.bouncycastle</groupId>
                        <artifactId>bcpkix-jdk15on</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.slf4j</groupId>
                        <artifactId>slf4j-api</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>us.zoom</groupId>
                <artifactId>csms-sdk-bridge-common</artifactId>
                <version>${csms.sdk.version}</version>
            </dependency>
            <dependency>
                <groupId>us.zoom</groupId>
                <artifactId>csms-sdk-api</artifactId>
                <version>${csms.sdk.version}</version>
            </dependency>
            <!--csms dependencies end-->
            <dependency>
                <groupId>org.apache.kafka</groupId>
                <artifactId>kafka-clients</artifactId>
                <version>3.9.0</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <profiles>
        <profile>
            <id>local</id>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
            <properties>
                <activatedProperties>local</activatedProperties>
            </properties>
        </profile>
        <profile>
            <id>dev</id>
            <properties>
                <activatedProperties>dev</activatedProperties>
            </properties>
        </profile>
        <profile>
            <id>prod</id>
            <properties>
                <activatedProperties>prod</activatedProperties>
            </properties>
        </profile>
    </profiles>

    <build>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <version>${compiler.plugin.version}</version>
                    <configuration>
                        <source>${maven.compiler.source}</source>
                        <target>${maven.compiler.target}</target>
                        <showDeprecation>true</showDeprecation>
                        <showWarnings>true</showWarnings>
                        <encoding>UTF-8</encoding>
                        <fork>true</fork>
                        <meminitial>256</meminitial>
                        <maxmem>1024</maxmem>
                        <compilerArgs>
                            <arg>-Xmaxwarns</arg>
                            <arg>10000</arg>
                            <arg>-Xlint</arg>
                            <arg>--add-exports</arg>
                            <arg>java.base/sun.security.rsa=ALL-UNNAMED</arg>
                            <arg>--add-exports</arg>
                            <arg>java.base/sun.security.util=ALL-UNNAMED</arg>
                        </compilerArgs>
                    </configuration>
                </plugin>
            </plugins>
        </pluginManagement>
    </build>
</project>